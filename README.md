# 🕉️ Ganesh Darshan

A minimal, clean platform for Ganesh Mandal listings with modern UI/UX design principles.

## ✨ Features

- **Clean Architecture**: Minimal design following modern UI/UX principles
- **User Management**: Simple registration with admin approval system
- **Mandal Listings**: Comprehensive mandal information with search and filters
- **Admin Dashboard**: Left sidebar navigation for user and mandal management
- **Responsive Design**: Mobile-first approach with Material-UI components
- **Secure Authentication**: JWT-based authentication without email verification
- **Professional Design**: Orange theme with clean typography and spacing

## 🏗️ Project Structure

```
ganesh-darshan/
├── backend/                 # Node.js Express API
│   ├── controllers/         # Route controllers
│   ├── models/             # MongoDB models
│   ├── routes/             # API routes
│   ├── middleware/         # Authentication middleware
│   └── server.js           # Main server file
├── frontend/               # React.js application
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── contexts/       # React contexts
│   │   ├── utils/          # Utility functions
│   │   └── types/          # TypeScript types
│   └── package.json
└── package.json           # Root package.json
```

## 🚀 Getting Started

### Prerequisites

- Node.js (v16 or higher)
- MongoDB
- npm or yarn

### Installation

1. **Clone and install dependencies:**
   ```bash
   npm run install-all
   ```

2. **Set up environment variables:**
   ```bash
   cd backend
   cp .env.example .env
   ```
   
   Edit `.env` with your configuration:
   ```env
   PORT=5000
   NODE_ENV=development
   MONGODB_URI=mongodb://localhost:27017/ganesh-darshan
   JWT_SECRET=your-super-secret-jwt-key-here
   JWT_EXPIRE=7d
   CLIENT_URL=http://localhost:3000
   ```

3. **Start development servers:**
   ```bash
   npm run dev
   ```

   This will start:
   - Backend API on http://localhost:5000
   - Frontend on http://localhost:3000

## 📱 Usage

### For Users
1. **Register**: Create an account (requires admin approval)
2. **Browse**: Explore Ganesh Mandals in your area
3. **Add Mandal**: Register your own mandal for approval
4. **Manage**: View and update your mandal listings

### For Admins
1. **Dashboard**: Access admin panel at `/admin`
2. **User Management**: Approve/reject user registrations
3. **Mandal Management**: Review and approve mandal submissions
4. **Statistics**: Monitor platform usage and pending approvals

## 🎨 Design Principles

- **Minimal**: Clean, uncluttered interface
- **Accessible**: WCAG compliant with proper contrast ratios
- **Mobile-First**: Responsive design for all devices
- **Consistent**: Unified color scheme and typography
- **Professional**: Business-appropriate styling

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile
- `PATCH /api/auth/profile` - Update user profile

### Mandals
- `GET /api/mandals` - Get all approved mandals
- `GET /api/mandals/:id` - Get mandal details
- `POST /api/mandals` - Create new mandal
- `GET /api/mandals/my/listings` - Get user's mandals

### Admin
- `GET /api/admin/dashboard/stats` - Dashboard statistics
- `GET /api/admin/users` - Get all users
- `PATCH /api/admin/users/:id/approve` - Approve user
- `PATCH /api/admin/users/:id/reject` - Reject user
- `GET /api/admin/mandals` - Get all mandals
- `PATCH /api/admin/mandals/:id/approve` - Approve mandal
- `PATCH /api/admin/mandals/:id/reject` - Reject mandal

## 🛠️ Technology Stack

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MongoDB** - Database
- **Mongoose** - ODM
- **JWT** - Authentication
- **bcryptjs** - Password hashing

### Frontend
- **React 18** - UI library
- **TypeScript** - Type safety
- **Material-UI** - Component library
- **React Router** - Navigation
- **Axios** - HTTP client
- **React Hook Form** - Form handling
- **React Hot Toast** - Notifications

## 📝 Scripts

```bash
# Development
npm run dev          # Start both frontend and backend
npm run server       # Start backend only
npm run client       # Start frontend only

# Installation
npm run install-all  # Install all dependencies

# Production
npm run build        # Build frontend
npm start           # Start production server
```

## 🔒 Security Features

- JWT-based authentication
- Password hashing with bcryptjs
- Input validation and sanitization
- CORS protection
- Rate limiting
- Helmet security headers

## 🎯 Key Features Implemented

✅ **Clean Project Structure**
✅ **User Authentication System**
✅ **Admin Approval Workflow**
✅ **Responsive Design**
✅ **Admin Dashboard with Sidebar**
✅ **Material-UI Theme**
✅ **TypeScript Support**
✅ **API Integration**

## 🚧 Future Enhancements

- Image upload for mandals
- Advanced search and filtering
- Google Maps integration
- Push notifications
- Mobile app
- Payment integration for donations

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

---

**Built with ❤️ for the Ganesh community**
