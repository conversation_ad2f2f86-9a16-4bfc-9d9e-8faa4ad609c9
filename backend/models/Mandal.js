const mongoose = require('mongoose');

const mandalSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Mandal name is required'],
    trim: true,
    maxlength: [100, 'Mandal name cannot exceed 100 characters']
  },
  registrationNumber: {
    type: String,
    required: [function() { return this.status !== 'draft'; }, 'Registration number is required'],
    unique: true,
    sparse: true,
    trim: true
  },
  establishmentYear: {
    type: Number,
    required: [function() { return this.status !== 'draft'; }, 'Establishment year is required'],
    min: [1800, 'Invalid establishment year'],
    max: [new Date().getFullYear(), 'Establishment year cannot be in the future']
  },
  description: {
    type: String,
    required: [function() { return this.status !== 'draft'; }, 'Description is required'],
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  category: {
    type: String,
    enum: ['mandal', 'home', 'celebrity'],
    required: [true, 'Category is required']
  },
  location: {
    address: {
      type: String,
      required: [true, 'Address is required']
    },
    city: {
      type: String,
      required: [true, 'City is required']
    },
    state: {
      type: String,
      required: [true, 'State is required']
    },
    pincode: {
      type: String,
      required: [true, 'Pincode is required'],
      match: [/^[1-9][0-9]{5}$/, 'Please enter a valid pincode']
    },
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  contactInfo: {
    email: {
      type: String,
      required: [function() { return this.parent().status !== 'draft'; }, 'Email address is required'],
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    phone: {
      type: String,
      required: [function() { return this.parent().status !== 'draft'; }, 'Primary phone number is required'],
      match: [/^[6-9]\d{9}$/, 'Please enter a valid phone number']
    },
    alternatePhone: {
      type: String,
      match: [/^[6-9]\d{9}$/, 'Please enter a valid phone number']
    },
    website: {
      type: String,
      match: [/^https?:\/\/.+/, 'Please enter a valid website URL']
    },
    socialMedia: {
      facebook: String,
      instagram: String,
      twitter: String,
      youtube: String
    }
  },
  teamMembers: [{
    name: {
      type: String,
      required: [function() { return this.parent().status !== 'draft'; }, 'Team member name is required']
    },
    designation: {
      type: String,
      required: [function() { return this.parent().status !== 'draft'; }, 'Team member designation is required']
    },
    phone: {
      type: String,
      match: [/^[6-9]\d{9}$/, 'Please enter a valid phone number']
    },
    photo: {
      type: String // URL to uploaded photo
    }
  }],
  ganeshDetails: {
    murtiType: {
      type: String,
      required: [function() { return this.parent().status !== 'draft'; }, 'Murti type is required'],
      enum: ['clay', 'plaster_of_paris', 'eco_friendly', 'fiber', 'metal', 'stone', 'other']
    },
    height: {
      type: String,
      required: [function() { return this.parent().status !== 'draft'; }, 'Ganesh height is required']
    },
    weight: {
      type: String,
      required: [function() { return this.parent().status !== 'draft'; }, 'Ganesh weight is required']
    },
    murtikarName: {
      type: String,
      trim: true,
      maxlength: [100, 'Murtikar name cannot exceed 100 characters']
    },
    festivalDuration: {
      type: String,
      required: [function() { return this.parent().status !== 'draft'; }, 'Festival duration is required'],
      enum: ['1.5', '3', '5', '7', '10']
    },
    decorationType: {
      type: String,
      required: [function() { return this.parent().status !== 'draft'; }, 'Decoration type is required'],
      enum: ['traditional', 'modern', 'theme_based', 'eco_friendly', 'cultural', 'mythological', 'other']
    },
    theme: {
      type: String,
      required: [function() { return this.parent().status !== 'draft'; }, 'Theme is required']
    },
    description: {
      type: String,
      required: [function() { return this.parent().status !== 'draft'; }, 'Description is required'],
      maxlength: [2000, 'Description cannot exceed 2000 characters']
    },
    specialFeatures: [String]
  },
  images: [{
    url: {
      type: String,
      required: true
    },
    caption: String,
    category: {
      type: String,
      enum: ['mandal', 'ganesh', 'decoration', 'events', 'team', 'other'],
      default: 'mandal'
    },
    year: {
      type: Number,
      default: new Date().getFullYear()
    },
    isPrimary: {
      type: Boolean,
      default: false
    }
  }],
  facilities: {
    parking: {
      type: Boolean,
      default: false
    },
    wheelchairAccess: {
      type: Boolean,
      default: false
    },
    restrooms: {
      type: Boolean,
      default: false
    },
    foodStalls: {
      type: Boolean,
      default: false
    }
  },
  timings: {
    openTime: {
      type: String,
      default: '06:00'
    },
    closeTime: {
      type: String,
      default: '22:00'
    },
    specialTimings: String
  },
  donations: {
    acceptsDonations: {
      type: Boolean,
      default: false
    },
    donationMethods: [String],
    bankDetails: {
      accountName: String,
      accountNumber: String,
      ifscCode: String,
      bankName: String
    }
  },
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  status: {
    type: String,
    enum: ['draft', 'pending', 'approved', 'rejected'],
    default: 'draft'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  views: {
    type: Number,
    default: 0
  },
  likes: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes for better performance
mandalSchema.index({ 'location.city': 1 });
mandalSchema.index({ category: 1 });
mandalSchema.index({ status: 1 });
mandalSchema.index({ owner: 1 });

module.exports = mongoose.model('Mandal', mandalSchema);
