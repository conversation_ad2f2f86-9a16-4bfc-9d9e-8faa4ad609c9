const { Mandal } = require('../models');
const { validationResult } = require('express-validator');

// Get all approved mandals (public)
exports.getAllMandals = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = { status: 'approved', isActive: true };
    
    if (req.query.category) {
      filter.category = req.query.category;
    }
    
    if (req.query.city) {
      filter['location.city'] = new RegExp(req.query.city, 'i');
    }

    if (req.query.search) {
      filter.$or = [
        { name: new RegExp(req.query.search, 'i') },
        { description: new RegExp(req.query.search, 'i') },
        { 'location.address': new RegExp(req.query.search, 'i') }
      ];
    }

    const mandals = await Mandal.find(filter)
      .populate('owner', 'firstName lastName')
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });

    const total = await Mandal.countDocuments(filter);

    res.json({
      status: 'success',
      data: {
        mandals,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get all mandals error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch mandals'
    });
  }
};

// Get single mandal by ID
exports.getMandal = async (req, res) => {
  try {
    const { id } = req.params;
    
    const mandal = await Mandal.findById(id)
      .populate('owner', 'firstName lastName phone');
    
    if (!mandal) {
      return res.status(404).json({
        status: 'error',
        message: 'Mandal not found'
      });
    }

    // Increment view count
    mandal.views += 1;
    await mandal.save();

    res.json({
      status: 'success',
      data: { mandal }
    });
  } catch (error) {
    console.error('Get mandal error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch mandal'
    });
  }
};

// Save mandal as draft (minimal validation)
exports.saveDraft = async (req, res) => {
  try {
    const mandalData = {
      ...req.body,
      owner: req.user.id,
      status: 'draft'
    };

    const mandal = await Mandal.create(mandalData);

    res.status(201).json({
      status: 'success',
      message: 'Draft saved successfully',
      data: { mandal }
    });
  } catch (error) {
    console.error('Save draft error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to save draft'
    });
  }
};

// Create new mandal (authenticated users)
exports.createMandal = async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        status: 'error',
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const mandalData = {
      ...req.body,
      owner: req.user.id,
      status: 'pending'
    };

    const mandal = await Mandal.create(mandalData);

    res.status(201).json({
      status: 'success',
      message: 'Mandal submitted for approval.',
      data: { mandal }
    });
  } catch (error) {
    console.error('Create mandal error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to create mandal'
    });
  }
};

// Get user's own mandals
exports.getMyMandals = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const mandals = await Mandal.find({ owner: req.user.id })
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });

    const total = await Mandal.countDocuments({ owner: req.user.id });

    res.json({
      status: 'success',
      data: {
        mandals,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Get my mandals error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch your mandals'
    });
  }
};

// Update mandal
exports.updateMandal = async (req, res) => {
  try {
    const { id } = req.params;
    
    const mandal = await Mandal.findOne({ _id: id, owner: req.user.id });
    
    if (!mandal) {
      return res.status(404).json({
        status: 'error',
        message: 'Mandal not found or you do not have permission to update it'
      });
    }

    const updatedMandal = await Mandal.findByIdAndUpdate(
      id,
      { ...req.body, updatedAt: Date.now() },
      { new: true, runValidators: true }
    );

    res.json({
      status: 'success',
      message: 'Mandal updated successfully',
      data: { mandal: updatedMandal }
    });
  } catch (error) {
    console.error('Update mandal error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to update mandal'
    });
  }
};

// Delete mandal
exports.deleteMandal = async (req, res) => {
  try {
    const { id } = req.params;
    
    const mandal = await Mandal.findOne({ _id: id, owner: req.user.id });
    
    if (!mandal) {
      return res.status(404).json({
        status: 'error',
        message: 'Mandal not found or you do not have permission to delete it'
      });
    }

    await Mandal.findByIdAndDelete(id);

    res.json({
      status: 'success',
      message: 'Mandal deleted successfully'
    });
  } catch (error) {
    console.error('Delete mandal error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to delete mandal'
    });
  }
};

// Like/Unlike mandal
exports.toggleLike = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    
    const mandal = await Mandal.findById(id);
    
    if (!mandal) {
      return res.status(404).json({
        status: 'error',
        message: 'Mandal not found'
      });
    }

    const isLiked = mandal.likes.includes(userId);
    
    if (isLiked) {
      mandal.likes.pull(userId);
    } else {
      mandal.likes.push(userId);
    }
    
    await mandal.save();

    res.json({
      status: 'success',
      message: isLiked ? 'Mandal unliked' : 'Mandal liked',
      data: {
        isLiked: !isLiked,
        likesCount: mandal.likes.length
      }
    });
  } catch (error) {
    console.error('Toggle like error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to toggle like'
    });
  }
};
