const express = require('express');
const { User } = require('../models');
const { generateToken } = require('../middleware/auth');

const router = express.Router();

// Initialize Super Admin (One-time setup or reset)
router.post('/init-admin', async (req, res) => {
  try {
    const adminEmail = '<EMAIL>';
    const adminPassword = 'admin123';

    // Check if super admin already exists
    let existingAdmin = await User.findOne({ email: adminEmail });

    if (existingAdmin) {
      // Update existing user to be super admin
      existingAdmin.role = 'superadmin';
      existingAdmin.isActive = true;
      existingAdmin.isApproved = true;
      existingAdmin.password = adminPassword; // This will be hashed by the pre-save middleware
      await existingAdmin.save();

      // Generate token
      const token = generateToken(existingAdmin._id);

      // Remove password from response
      existingAdmin.password = undefined;

      return res.json({
        status: 'success',
        message: 'Super admin updated successfully',
        data: {
          user: existingAdmin,
          token,
          credentials: {
            email: adminEmail,
            password: adminPassword
          }
        }
      });
    }

    // Create new super admin
    const superAdmin = await User.create({
      firstName: 'Super',
      lastName: 'Admin',
      email: adminEmail,
      phone: '9999999999',
      password: adminPassword,
      role: 'superadmin',
      isActive: true,
      isApproved: true,
      address: {
        city: 'Mumbai',
        state: 'Maharashtra'
      }
    });

    // Generate token
    const token = generateToken(superAdmin._id);

    // Remove password from response
    superAdmin.password = undefined;

    res.status(201).json({
      status: 'success',
      message: 'Super admin created successfully',
      data: {
        user: superAdmin,
        token,
        credentials: {
          email: adminEmail,
          password: adminPassword
        }
      }
    });
  } catch (error) {
    console.error('Super admin creation error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to create super admin',
      error: error.message
    });
  }
});

// Reset admin password
router.post('/reset-admin', async (req, res) => {
  try {
    const adminEmail = '<EMAIL>';
    const newPassword = 'admin123';

    const admin = await User.findOne({ email: adminEmail });

    if (!admin) {
      return res.status(404).json({
        status: 'error',
        message: 'Admin user not found'
      });
    }

    // Reset password and ensure admin privileges
    admin.password = newPassword;
    admin.role = 'superadmin';
    admin.isActive = true;
    admin.isApproved = true;
    await admin.save();

    res.json({
      status: 'success',
      message: 'Admin password reset successfully',
      data: {
        credentials: {
          email: adminEmail,
          password: newPassword
        }
      }
    });
  } catch (error) {
    console.error('Reset admin error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to reset admin password'
    });
  }
});

// Get setup status
router.get('/status', async (req, res) => {
  try {
    const adminExists = await User.findOne({ role: 'superadmin' });
    const anyAdminEmail = await User.findOne({ email: '<EMAIL>' });

    res.json({
      status: 'success',
      data: {
        adminExists: !!adminExists,
        setupRequired: !adminExists,
        emailExists: !!anyAdminEmail,
        needsReset: !!anyAdminEmail && !adminExists
      }
    });
  } catch (error) {
    console.error('Setup status error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to check setup status'
    });
  }
});

module.exports = router;
