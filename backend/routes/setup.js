const express = require('express');
const { User } = require('../models');
const { generateToken } = require('../middleware/auth');

const router = express.Router();

// Initialize Super Admin (One-time setup)
router.post('/init-admin', async (req, res) => {
  try {
    // Check if super admin already exists
    const existingAdmin = await User.findOne({ role: 'superadmin' });
    
    if (existingAdmin) {
      return res.status(400).json({
        status: 'error',
        message: 'Super admin already exists'
      });
    }

    // Create super admin with default credentials
    const superAdmin = await User.create({
      firstName: 'Super',
      lastName: 'Admin',
      email: '<EMAIL>',
      phone: '9999999999',
      password: 'admin123', // Default password - should be changed after first login
      role: 'superadmin',
      isActive: true,
      isApproved: true,
      address: {
        city: 'Mumbai',
        state: 'Maharashtra'
      }
    });

    // Generate token
    const token = generateToken(superAdmin._id);

    // Remove password from response
    superAdmin.password = undefined;

    res.status(201).json({
      status: 'success',
      message: 'Super admin created successfully',
      data: {
        user: superAdmin,
        token,
        credentials: {
          email: '<EMAIL>',
          password: 'admin123'
        }
      }
    });
  } catch (error) {
    console.error('Super admin creation error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to create super admin'
    });
  }
});

// Get setup status
router.get('/status', async (req, res) => {
  try {
    const adminExists = await User.findOne({ role: 'superadmin' });
    
    res.json({
      status: 'success',
      data: {
        adminExists: !!adminExists,
        setupRequired: !adminExists
      }
    });
  } catch (error) {
    console.error('Setup status error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to check setup status'
    });
  }
});

module.exports = router;
