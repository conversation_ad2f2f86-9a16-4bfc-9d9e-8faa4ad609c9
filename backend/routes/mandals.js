const express = require('express');
const { body } = require('express-validator');
const mandalController = require('../controllers/mandalController');
const { protect, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// Validation rules for mandal creation
const mandalValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Mandal name must be between 2 and 100 characters'),
  body('registrationNumber')
    .trim()
    .notEmpty()
    .withMessage('Registration number is required'),
  body('establishmentYear')
    .isInt({ min: 1800, max: new Date().getFullYear() })
    .withMessage('Please provide a valid establishment year'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Description must be between 10 and 1000 characters'),
  body('category')
    .isIn(['mandal', 'home', 'celebrity'])
    .withMessage('Category must be mandal, home, or celebrity'),
  body('location.address')
    .trim()
    .notEmpty()
    .withMessage('Address is required'),
  body('location.city')
    .trim()
    .notEmpty()
    .withMessage('City is required'),
  body('location.state')
    .trim()
    .notEmpty()
    .withMessage('State is required'),
  body('location.pincode')
    .matches(/^[1-9][0-9]{5}$/)
    .withMessage('Please provide a valid pincode'),
  body('contactInfo.email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('contactInfo.phone')
    .matches(/^[6-9]\d{9}$/)
    .withMessage('Please provide a valid phone number'),
  body('ganeshDetails.murtiType')
    .isIn(['clay', 'plaster_of_paris', 'eco_friendly', 'fiber', 'metal', 'stone', 'other'])
    .withMessage('Please select a valid murti type'),
  body('ganeshDetails.height')
    .trim()
    .notEmpty()
    .withMessage('Ganesh height is required'),
  body('ganeshDetails.weight')
    .trim()
    .notEmpty()
    .withMessage('Ganesh weight is required'),
  body('ganeshDetails.decorationType')
    .isIn(['traditional', 'modern', 'theme_based', 'eco_friendly', 'cultural', 'mythological', 'other'])
    .withMessage('Please select a valid decoration type'),
  body('ganeshDetails.theme')
    .trim()
    .notEmpty()
    .withMessage('Theme is required'),
  body('ganeshDetails.description')
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Description must be between 10 and 2000 characters'),
  body('timings.openTime')
    .trim()
    .notEmpty()
    .withMessage('Opening time is required'),
  body('timings.closeTime')
    .trim()
    .notEmpty()
    .withMessage('Closing time is required')
];

// Public routes
router.get('/', optionalAuth, mandalController.getAllMandals);
router.get('/:id', optionalAuth, mandalController.getMandal);

// Protected routes - require authentication
router.use(protect);

// User routes
router.post('/', mandalValidation, mandalController.createMandal);
router.get('/my/listings', mandalController.getMyMandals);
router.patch('/:id', mandalController.updateMandal);
router.delete('/:id', mandalController.deleteMandal);

// Like/Unlike routes
router.post('/:id/like', mandalController.toggleLike);

module.exports = router;
