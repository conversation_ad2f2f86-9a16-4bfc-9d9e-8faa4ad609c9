import axios from 'axios';
import { LoginData, RegisterData, ApiResponse, User, Mandal } from '../types';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const authAPI = {
  login: (data: LoginData) => api.post('/auth/login', data),
  register: (data: RegisterData) => api.post('/auth/register', data),
  getProfile: () => api.get('/auth/profile'),
  updateProfile: (data: Partial<User>) => api.patch('/auth/profile', data),
};

export const mandalAPI = {
  getAll: (params?: any) => api.get('/mandals', { params }),
  getById: (id: string) => api.get(`/mandals/${id}`),
  create: (data: Partial<Mandal>) => api.post('/mandals', data),
  update: (id: string, data: Partial<Mandal>) => api.patch(`/mandals/${id}`, data),
  delete: (id: string) => api.delete(`/mandals/${id}`),
  getMyMandals: (params?: any) => api.get('/mandals/my/listings', { params }),
  toggleLike: (id: string) => api.post(`/mandals/${id}/like`),
};

export const adminAPI = {
  getDashboardStats: () => api.get('/admin/dashboard/stats'),
  getAllUsers: (params?: any) => api.get('/admin/users', { params }),
  approveUser: (userId: string) => api.patch(`/admin/users/${userId}/approve`),
  rejectUser: (userId: string) => api.patch(`/admin/users/${userId}/reject`),
  getAllMandals: (params?: any) => api.get('/admin/mandals', { params }),
  approveMandal: (mandalId: string) => api.patch(`/admin/mandals/${mandalId}/approve`),
  rejectMandal: (mandalId: string, reason?: string) => 
    api.patch(`/admin/mandals/${mandalId}/reject`, { reason }),
};

export const userAPI = {
  getAll: (params?: any) => api.get('/users', { params }),
  getById: (id: string) => api.get(`/users/${id}`),
};

export default api;
