{"name": "g<PERSON><PERSON>-da<PERSON><PERSON>-frontend", "version": "1.0.0", "description": "Clean frontend for <PERSON><PERSON><PERSON> platform", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "@mui/material": "^5.11.0", "@mui/icons-material": "^5.11.0", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "axios": "^1.3.0", "react-hook-form": "^7.43.0", "react-hot-toast": "^2.4.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@vitejs/plugin-react": "^3.1.0", "typescript": "^4.9.0", "vite": "^4.1.0"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "start": "vite"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}