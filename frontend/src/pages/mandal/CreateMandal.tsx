import React, { useState } from 'react';
import {
  Container,
  Paper,
  Typography,
  <PERSON>,
  Stepper,
  Step,
  StepLabel,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Alert,
  Divider,
  Card,
  CardContent,
  InputAdornment,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  CloudUpload as UploadIcon,
  Facebook,
  Instagram,
  Twitter,
  YouTube,
  Language as WebsiteIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
} from '@mui/icons-material';
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { mandalAPI } from '../../utils/api';
import toast from 'react-hot-toast';

interface MandalFormData {
  // Basic Information
  name: string;
  registrationNumber: string;
  establishmentYear: number;
  description: string;
  category: 'mandal' | 'home' | 'celebrity';

  // Location
  location: {
    address: string;
    city: string;
    state: string;
    pincode: string;
  };

  // Contact Information
  contactInfo: {
    email: string;
    phone: string;
    alternatePhone?: string;
    website?: string;
    socialMedia: {
      facebook?: string;
      instagram?: string;
      twitter?: string;
      youtube?: string;
    };
  };

  // Team Members
  teamMembers: Array<{
    name: string;
    designation: string;
    phone?: string;
  }>;

  // Ganesh Details
  ganeshDetails: {
    murtiType: string;
    height: string;
    weight: string;
    decorationType: string;
    theme: string;
    description: string;
    specialFeatures: string[];
  };

  // Facilities & Timings
  facilities: {
    parking: boolean;
    wheelchairAccess: boolean;
    restrooms: boolean;
    foodStalls: boolean;
  };

  timings: {
    openTime: string;
    closeTime: string;
    specialTimings?: string;
  };

  // Donations
  donations: {
    acceptsDonations: boolean;
    donationMethods: string[];
    bankDetails?: {
      accountName: string;
      accountNumber: string;
      ifscCode: string;
      bankName: string;
    };
  };
}

const steps = [
  'Basic Information',
  'Contact Details',
  'Team Members',
  'Ganesh Details',
  'Additional Information'
];

const CreateMandal: React.FC = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    trigger,
  } = useForm<MandalFormData>({
    defaultValues: {
      teamMembers: [{ name: '', designation: '', phone: '' }],
      ganeshDetails: {
        specialFeatures: [],
      },
      facilities: {
        parking: false,
        wheelchairAccess: false,
        restrooms: false,
        foodStalls: false,
      },
      donations: {
        acceptsDonations: false,
        donationMethods: [],
      },
      contactInfo: {
        socialMedia: {},
      },
    },
  });

  const { fields: teamFields, append: appendTeam, remove: removeTeam } = useFieldArray({
    control,
    name: 'teamMembers',
  });

  const handleNext = async () => {
    const fieldsToValidate = getFieldsForStep(activeStep);
    const isValid = await trigger(fieldsToValidate);

    if (isValid) {
      setActiveStep((prevStep) => prevStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const getFieldsForStep = (step: number): (keyof MandalFormData)[] => {
    switch (step) {
      case 0:
        return ['name', 'registrationNumber', 'establishmentYear', 'category', 'location'];
      case 1:
        return ['contactInfo'];
      case 2:
        return ['teamMembers'];
      case 3:
        return ['ganeshDetails'];
      case 4:
        return ['facilities', 'timings', 'donations'];
      default:
        return [];
    }
  };

  const onSubmit = async (data: MandalFormData) => {
    try {
      setLoading(true);
      await mandalAPI.create(data);
      toast.success('Mandal registered successfully! Waiting for admin approval.');
      navigate('/my-mandals');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to register mandal');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Paper elevation={2} sx={{ p: 4 }}>
        <Typography variant="h4" gutterBottom align="center" color="primary">
          🕉️ Register Your Mandal
        </Typography>
        <Typography variant="body1" align="center" color="text.secondary" sx={{ mb: 4 }}>
          Share your Ganesh celebration with devotees across the community
        </Typography>

        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        <form onSubmit={handleSubmit(onSubmit)}>
          {activeStep === 0 && (
            <BasicInformationStep control={control} errors={errors} />
          )}
          {activeStep === 1 && (
            <ContactDetailsStep control={control} errors={errors} />
          )}
          {activeStep === 2 && (
            <TeamMembersStep
              control={control}
              errors={errors}
              fields={teamFields}
              append={appendTeam}
              remove={removeTeam}
            />
          )}
          {activeStep === 3 && (
            <GaneshDetailsStep control={control} errors={errors} setValue={setValue} watch={watch} />
          )}
          {activeStep === 4 && (
            <AdditionalInformationStep control={control} errors={errors} />
          )}

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
            <Button
              disabled={activeStep === 0}
              onClick={handleBack}
              variant="outlined"
            >
              Back
            </Button>

            {activeStep === steps.length - 1 ? (
              <Button
                type="submit"
                variant="contained"
                disabled={loading}
                size="large"
              >
                {loading ? 'Registering...' : 'Register Mandal'}
              </Button>
            ) : (
              <Button
                onClick={handleNext}
                variant="contained"
                size="large"
              >
                Next
              </Button>
            )}
          </Box>
        </form>
      </Paper>
    </Container>
  );
};

// Step Components
const BasicInformationStep: React.FC<{
  control: any;
  errors: any;
}> = ({ control, errors }) => (
  <Box>
    <Typography variant="h6" gutterBottom color="primary">
      Basic Information
    </Typography>
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Controller
          name="name"
          control={control}
          rules={{ required: 'Mandal name is required' }}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              label="Mandal Name"
              error={!!errors.name}
              helperText={errors.name?.message}
              placeholder="Enter your mandal name"
            />
          )}
        />
      </Grid>

      <Grid item xs={12} md={6}>
        <Controller
          name="registrationNumber"
          control={control}
          rules={{ required: 'Registration number is required' }}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              label="Registration Number"
              error={!!errors.registrationNumber}
              helperText={errors.registrationNumber?.message}
              placeholder="Enter registration number"
            />
          )}
        />
      </Grid>

      <Grid item xs={12} md={6}>
        <Controller
          name="establishmentYear"
          control={control}
          rules={{
            required: 'Establishment year is required',
            min: { value: 1800, message: 'Invalid year' },
            max: { value: new Date().getFullYear(), message: 'Year cannot be in future' }
          }}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              type="number"
              label="Establishment Year"
              error={!!errors.establishmentYear}
              helperText={errors.establishmentYear?.message}
              placeholder="e.g., 1995"
            />
          )}
        />
      </Grid>

      <Grid item xs={12} md={6}>
        <Controller
          name="category"
          control={control}
          rules={{ required: 'Category is required' }}
          render={({ field }) => (
            <FormControl fullWidth error={!!errors.category}>
              <InputLabel>Category</InputLabel>
              <Select {...field} label="Category">
                <MenuItem value="mandal">Mandal</MenuItem>
                <MenuItem value="home">Home</MenuItem>
                <MenuItem value="celebrity">Celebrity</MenuItem>
              </Select>
            </FormControl>
          )}
        />
      </Grid>

      <Grid item xs={12}>
        <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
          Location Details
        </Typography>
      </Grid>

      <Grid item xs={12}>
        <Controller
          name="location.address"
          control={control}
          rules={{ required: 'Address is required' }}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              multiline
              rows={2}
              label="Complete Address"
              error={!!errors.location?.address}
              helperText={errors.location?.address?.message}
              placeholder="Enter complete address with landmarks"
            />
          )}
        />
      </Grid>

      <Grid item xs={12} md={4}>
        <Controller
          name="location.city"
          control={control}
          rules={{ required: 'City is required' }}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              label="City"
              error={!!errors.location?.city}
              helperText={errors.location?.city?.message}
            />
          )}
        />
      </Grid>

      <Grid item xs={12} md={4}>
        <Controller
          name="location.state"
          control={control}
          rules={{ required: 'State is required' }}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              label="State"
              error={!!errors.location?.state}
              helperText={errors.location?.state?.message}
            />
          )}
        />
      </Grid>

      <Grid item xs={12} md={4}>
        <Controller
          name="location.pincode"
          control={control}
          rules={{
            required: 'Pincode is required',
            pattern: { value: /^[1-9][0-9]{5}$/, message: 'Invalid pincode' }
          }}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              label="Pincode"
              error={!!errors.location?.pincode}
              helperText={errors.location?.pincode?.message}
            />
          )}
        />
      </Grid>
    </Grid>
  </Box>
);

const ContactDetailsStep: React.FC<{
  control: any;
  errors: any;
}> = ({ control, errors }) => (
  <Box>
    <Typography variant="h6" gutterBottom color="primary">
      Contact Information
    </Typography>
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Controller
          name="contactInfo.email"
          control={control}
          rules={{
            required: 'Email is required',
            pattern: { value: /^\S+@\S+$/i, message: 'Invalid email' }
          }}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              type="email"
              label="Email Address"
              error={!!errors.contactInfo?.email}
              helperText={errors.contactInfo?.email?.message}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <EmailIcon color="primary" />
                  </InputAdornment>
                ),
              }}
            />
          )}
        />
      </Grid>

      <Grid item xs={12} md={6}>
        <Controller
          name="contactInfo.phone"
          control={control}
          rules={{
            required: 'Phone number is required',
            pattern: { value: /^[6-9]\d{9}$/, message: 'Invalid phone number' }
          }}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              label="Primary Phone Number"
              error={!!errors.contactInfo?.phone}
              helperText={errors.contactInfo?.phone?.message}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PhoneIcon color="primary" />
                  </InputAdornment>
                ),
              }}
            />
          )}
        />
      </Grid>

      <Grid item xs={12} md={6}>
        <Controller
          name="contactInfo.alternatePhone"
          control={control}
          rules={{
            pattern: { value: /^[6-9]\d{9}$/, message: 'Invalid phone number' }
          }}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              label="Alternate Phone Number (Optional)"
              error={!!errors.contactInfo?.alternatePhone}
              helperText={errors.contactInfo?.alternatePhone?.message}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PhoneIcon color="secondary" />
                  </InputAdornment>
                ),
              }}
            />
          )}
        />
      </Grid>

      <Grid item xs={12} md={6}>
        <Controller
          name="contactInfo.website"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              label="Website (Optional)"
              placeholder="https://your-website.com"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <WebsiteIcon color="primary" />
                  </InputAdornment>
                ),
              }}
            />
          )}
        />
      </Grid>

      <Grid item xs={12}>
        <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
          Social Media Links (Optional)
        </Typography>
      </Grid>

      <Grid item xs={12} md={6}>
        <Controller
          name="contactInfo.socialMedia.facebook"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              label="Facebook Page"
              placeholder="https://facebook.com/your-page"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Facebook sx={{ color: '#1877F2' }} />
                  </InputAdornment>
                ),
              }}
            />
          )}
        />
      </Grid>

      <Grid item xs={12} md={6}>
        <Controller
          name="contactInfo.socialMedia.instagram"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              label="Instagram Profile"
              placeholder="https://instagram.com/your-profile"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Instagram sx={{ color: '#E4405F' }} />
                  </InputAdornment>
                ),
              }}
            />
          )}
        />
      </Grid>

      <Grid item xs={12} md={6}>
        <Controller
          name="contactInfo.socialMedia.twitter"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              label="Twitter Profile"
              placeholder="https://twitter.com/your-profile"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Twitter sx={{ color: '#1DA1F2' }} />
                  </InputAdornment>
                ),
              }}
            />
          )}
        />
      </Grid>

      <Grid item xs={12} md={6}>
        <Controller
          name="contactInfo.socialMedia.youtube"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              label="YouTube Channel"
              placeholder="https://youtube.com/your-channel"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <YouTube sx={{ color: '#FF0000' }} />
                  </InputAdornment>
                ),
              }}
            />
          )}
        />
      </Grid>
    </Grid>
  </Box>
);

const TeamMembersStep: React.FC<{
  control: any;
  errors: any;
  fields: any[];
  append: (value: any) => void;
  remove: (index: number) => void;
}> = ({ control, errors, fields, append, remove }) => (
  <Box>
    <Typography variant="h6" gutterBottom color="primary">
      Team Members
    </Typography>
    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
      Add your mandal team members with their designations
    </Typography>

    {fields.map((field, index) => (
      <Card key={field.id} sx={{ mb: 2, p: 2 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="subtitle1">
              Team Member {index + 1}
            </Typography>
            {fields.length > 1 && (
              <IconButton
                onClick={() => remove(index)}
                color="error"
                size="small"
              >
                <DeleteIcon />
              </IconButton>
            )}
          </Box>

          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <Controller
                name={`teamMembers.${index}.name`}
                control={control}
                rules={{ required: 'Name is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Full Name"
                    error={!!errors.teamMembers?.[index]?.name}
                    helperText={errors.teamMembers?.[index]?.name?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <Controller
                name={`teamMembers.${index}.designation`}
                control={control}
                rules={{ required: 'Designation is required' }}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.teamMembers?.[index]?.designation}>
                    <InputLabel>Designation</InputLabel>
                    <Select {...field} label="Designation">
                      <MenuItem value="President">President</MenuItem>
                      <MenuItem value="Vice President">Vice President</MenuItem>
                      <MenuItem value="Secretary">Secretary</MenuItem>
                      <MenuItem value="Treasurer">Treasurer</MenuItem>
                      <MenuItem value="Joint Secretary">Joint Secretary</MenuItem>
                      <MenuItem value="Committee Member">Committee Member</MenuItem>
                      <MenuItem value="Coordinator">Coordinator</MenuItem>
                      <MenuItem value="Volunteer">Volunteer</MenuItem>
                      <MenuItem value="Other">Other</MenuItem>
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <Controller
                name={`teamMembers.${index}.phone`}
                control={control}
                rules={{
                  pattern: { value: /^[6-9]\d{9}$/, message: 'Invalid phone number' }
                }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Phone Number (Optional)"
                    error={!!errors.teamMembers?.[index]?.phone}
                    helperText={errors.teamMembers?.[index]?.phone?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    ))}

    <Button
      onClick={() => append({ name: '', designation: '', phone: '' })}
      startIcon={<AddIcon />}
      variant="outlined"
      sx={{ mt: 2 }}
    >
      Add Team Member
    </Button>
  </Box>
);

const GaneshDetailsStep: React.FC<{
  control: any;
  errors: any;
  setValue: any;
  watch: any;
}> = ({ control, errors, setValue, watch }) => {
  const specialFeatures = watch('ganeshDetails.specialFeatures') || [];

  const addSpecialFeature = (feature: string) => {
    if (feature && !specialFeatures.includes(feature)) {
      setValue('ganeshDetails.specialFeatures', [...specialFeatures, feature]);
    }
  };

  const removeSpecialFeature = (feature: string) => {
    setValue('ganeshDetails.specialFeatures', specialFeatures.filter((f: string) => f !== feature));
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom color="primary">
        Current Year Ganesha Details
      </Typography>
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Controller
            name="ganeshDetails.murtiType"
            control={control}
            rules={{ required: 'Murti type is required' }}
            render={({ field }) => (
              <FormControl fullWidth error={!!errors.ganeshDetails?.murtiType}>
                <InputLabel>Murti Type</InputLabel>
                <Select {...field} label="Murti Type">
                  <MenuItem value="clay">Clay (Eco-friendly)</MenuItem>
                  <MenuItem value="plaster_of_paris">Plaster of Paris</MenuItem>
                  <MenuItem value="eco_friendly">Eco-friendly Material</MenuItem>
                  <MenuItem value="fiber">Fiber</MenuItem>
                  <MenuItem value="metal">Metal</MenuItem>
                  <MenuItem value="stone">Stone</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="ganeshDetails.decorationType"
            control={control}
            rules={{ required: 'Decoration type is required' }}
            render={({ field }) => (
              <FormControl fullWidth error={!!errors.ganeshDetails?.decorationType}>
                <InputLabel>Decoration Type</InputLabel>
                <Select {...field} label="Decoration Type">
                  <MenuItem value="traditional">Traditional</MenuItem>
                  <MenuItem value="modern">Modern</MenuItem>
                  <MenuItem value="theme_based">Theme Based</MenuItem>
                  <MenuItem value="eco_friendly">Eco-friendly</MenuItem>
                  <MenuItem value="cultural">Cultural</MenuItem>
                  <MenuItem value="mythological">Mythological</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="ganeshDetails.height"
            control={control}
            rules={{ required: 'Height is required' }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Murti Height"
                placeholder="e.g., 5 feet, 10 feet"
                error={!!errors.ganeshDetails?.height}
                helperText={errors.ganeshDetails?.height?.message}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="ganeshDetails.weight"
            control={control}
            rules={{ required: 'Weight is required' }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Murti Weight"
                placeholder="e.g., 50 kg, 100 kg"
                error={!!errors.ganeshDetails?.weight}
                helperText={errors.ganeshDetails?.weight?.message}
              />
            )}
          />
        </Grid>

        <Grid item xs={12}>
          <Controller
            name="ganeshDetails.theme"
            control={control}
            rules={{ required: 'Theme is required' }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Theme"
                placeholder="e.g., Eco-friendly, Traditional, Modern Art"
                error={!!errors.ganeshDetails?.theme}
                helperText={errors.ganeshDetails?.theme?.message}
              />
            )}
          />
        </Grid>

        <Grid item xs={12}>
          <Controller
            name="ganeshDetails.description"
            control={control}
            rules={{
              required: 'Description is required',
              maxLength: { value: 2000, message: 'Description cannot exceed 2000 characters' }
            }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                multiline
                rows={4}
                label="Description"
                placeholder="Describe your Ganesha, decoration, special features, and what makes it unique..."
                error={!!errors.ganeshDetails?.description}
                helperText={errors.ganeshDetails?.description?.message}
              />
            )}
          />
        </Grid>

        <Grid item xs={12}>
          <Typography variant="subtitle1" gutterBottom>
            Special Features
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
            {specialFeatures.map((feature: string) => (
              <Chip
                key={feature}
                label={feature}
                onDelete={() => removeSpecialFeature(feature)}
                color="primary"
                variant="outlined"
              />
            ))}
          </Box>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {['Live Darshan', 'Cultural Programs', 'Food Distribution', 'Medical Camp', 'Blood Donation', 'Environmental Initiative'].map((feature) => (
              <Button
                key={feature}
                size="small"
                variant="outlined"
                onClick={() => addSpecialFeature(feature)}
                disabled={specialFeatures.includes(feature)}
              >
                {feature}
              </Button>
            ))}
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

const AdditionalInformationStep: React.FC<{
  control: any;
  errors: any;
}> = ({ control, errors }) => (
  <Box>
    <Typography variant="h6" gutterBottom color="primary">
      Additional Information
    </Typography>

    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="subtitle1" gutterBottom>
          Facilities Available
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={6} md={3}>
            <Controller
              name="facilities.parking"
              control={control}
              render={({ field }) => (
                <Box>
                  <input
                    type="checkbox"
                    {...field}
                    checked={field.value}
                    id="parking"
                  />
                  <label htmlFor="parking" style={{ marginLeft: 8 }}>
                    Parking Available
                  </label>
                </Box>
              )}
            />
          </Grid>
          <Grid item xs={6} md={3}>
            <Controller
              name="facilities.wheelchairAccess"
              control={control}
              render={({ field }) => (
                <Box>
                  <input
                    type="checkbox"
                    {...field}
                    checked={field.value}
                    id="wheelchair"
                  />
                  <label htmlFor="wheelchair" style={{ marginLeft: 8 }}>
                    Wheelchair Access
                  </label>
                </Box>
              )}
            />
          </Grid>
          <Grid item xs={6} md={3}>
            <Controller
              name="facilities.restrooms"
              control={control}
              render={({ field }) => (
                <Box>
                  <input
                    type="checkbox"
                    {...field}
                    checked={field.value}
                    id="restrooms"
                  />
                  <label htmlFor="restrooms" style={{ marginLeft: 8 }}>
                    Restrooms
                  </label>
                </Box>
              )}
            />
          </Grid>
          <Grid item xs={6} md={3}>
            <Controller
              name="facilities.foodStalls"
              control={control}
              render={({ field }) => (
                <Box>
                  <input
                    type="checkbox"
                    {...field}
                    checked={field.value}
                    id="food"
                  />
                  <label htmlFor="food" style={{ marginLeft: 8 }}>
                    Food Stalls
                  </label>
                </Box>
              )}
            />
          </Grid>
        </Grid>
      </Grid>

      <Grid item xs={12}>
        <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
          Darshan Timings
        </Typography>
      </Grid>

      <Grid item xs={12} md={6}>
        <Controller
          name="timings.openTime"
          control={control}
          rules={{ required: 'Opening time is required' }}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              type="time"
              label="Opening Time"
              error={!!errors.timings?.openTime}
              helperText={errors.timings?.openTime?.message}
              InputLabelProps={{ shrink: true }}
            />
          )}
        />
      </Grid>

      <Grid item xs={12} md={6}>
        <Controller
          name="timings.closeTime"
          control={control}
          rules={{ required: 'Closing time is required' }}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              type="time"
              label="Closing Time"
              error={!!errors.timings?.closeTime}
              helperText={errors.timings?.closeTime?.message}
              InputLabelProps={{ shrink: true }}
            />
          )}
        />
      </Grid>

      <Grid item xs={12}>
        <Controller
          name="timings.specialTimings"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              multiline
              rows={2}
              label="Special Timings (Optional)"
              placeholder="e.g., Extended hours on festival days, Special aarti timings"
            />
          )}
        />
      </Grid>

      <Grid item xs={12}>
        <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
          Donations
        </Typography>
      </Grid>

      <Grid item xs={12}>
        <Controller
          name="donations.acceptsDonations"
          control={control}
          render={({ field }) => (
            <Box>
              <input
                type="checkbox"
                {...field}
                checked={field.value}
                id="donations"
              />
              <label htmlFor="donations" style={{ marginLeft: 8 }}>
                Accept Donations
              </label>
            </Box>
          )}
        />
      </Grid>
    </Grid>
  </Box>
);

export default CreateMandal;
