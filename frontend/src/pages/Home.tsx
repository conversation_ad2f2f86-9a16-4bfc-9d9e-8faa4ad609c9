import React from 'react';
import {
  Container,
  Typo<PERSON>,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Paper,
} from '@mui/material';
import {
  AccountBalance as Temple,
  People,
  Favorite,
  Search,
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const Home: React.FC = () => {
  const { user } = useAuth();

  const features = [
    {
      icon: <Temple sx={{ fontSize: 40, color: 'primary.main' }} />,
      title: 'Discover Mandals',
      description: 'Explore Ganesh Mandals in your city and beyond. Find detailed information about each celebration.',
    },
    {
      icon: <People sx={{ fontSize: 40, color: 'primary.main' }} />,
      title: 'Connect Communities',
      description: 'Join a vibrant community of devotees and mandal organizers celebrating Lord Ganesha.',
    },
    {
      icon: <Favorite sx={{ fontSize: 40, color: 'primary.main' }} />,
      title: 'Share Devotion',
      description: 'Share your mandal with the world and let devotees experience the divine presence.',
    },
    {
      icon: <Search sx={{ fontSize: 40, color: 'primary.main' }} />,
      title: 'Easy Search',
      description: 'Find mandals by location, theme, or special features with our intuitive search system.',
    },
  ];

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)',
          color: 'white',
          py: 8,
          textAlign: 'center',
        }}
      >
        <Container maxWidth="md">
          <Typography variant="h2" component="h1" gutterBottom fontWeight="bold">
            🕉️ Ganesh Darshan
          </Typography>
          <Typography variant="h5" sx={{ mb: 4, opacity: 0.9 }}>
            Connecting Devotees with Divine Celebrations
          </Typography>
          <Typography variant="body1" sx={{ mb: 4, fontSize: '1.1rem' }}>
            Discover Ganesh Mandals in your area, explore beautiful decorations, 
            and be part of the grand celebration of Lord Ganesha.
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              component={Link}
              to="/mandals"
              variant="contained"
              size="large"
              sx={{
                bgcolor: 'white',
                color: 'primary.main',
                '&:hover': {
                  bgcolor: 'grey.100',
                },
              }}
            >
              Explore Mandals
            </Button>
            {!user && (
              <Button
                component={Link}
                to="/register"
                variant="outlined"
                size="large"
                sx={{
                  borderColor: 'white',
                  color: 'white',
                  '&:hover': {
                    borderColor: 'white',
                    bgcolor: 'rgba(255,255,255,0.1)',
                  },
                }}
              >
                Register Your Mandal
              </Button>
            )}
            {user && (
              <Button
                component={Link}
                to="/create-mandal"
                variant="outlined"
                size="large"
                sx={{
                  borderColor: 'white',
                  color: 'white',
                  '&:hover': {
                    borderColor: 'white',
                    bgcolor: 'rgba(255,255,255,0.1)',
                  },
                }}
              >
                Add Your Mandal
              </Button>
            )}
          </Box>
        </Container>
      </Box>

      {/* Features Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Typography variant="h3" component="h2" textAlign="center" gutterBottom>
          Why Choose Ganesh Darshan?
        </Typography>
        <Typography
          variant="body1"
          textAlign="center"
          color="text.secondary"
          sx={{ mb: 6, maxWidth: 600, mx: 'auto' }}
        >
          Our platform brings together mandal organizers and devotees, 
          creating a seamless experience for celebrating Ganesh Chaturthi.
        </Typography>

        <Grid container spacing={4}>
          {features.map((feature, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card
                sx={{
                  height: '100%',
                  textAlign: 'center',
                  transition: 'transform 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                  },
                }}
              >
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ mb: 2 }}>
                    {feature.icon}
                  </Box>
                  <Typography variant="h6" component="h3" gutterBottom>
                    {feature.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {feature.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Call to Action Section */}
      <Paper
        sx={{
          bgcolor: 'grey.50',
          py: 6,
        }}
      >
        <Container maxWidth="md" sx={{ textAlign: 'center' }}>
          <Typography variant="h4" component="h2" gutterBottom>
            Ready to Join the Celebration?
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
            Whether you're organizing a mandal or looking to visit one, 
            Ganesh Darshan is your gateway to divine experiences.
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              component={Link}
              to="/mandals"
              variant="contained"
              size="large"
            >
              Browse Mandals
            </Button>
            {user ? (
              <Button
                component={Link}
                to="/create-mandal"
                variant="outlined"
                size="large"
              >
                Add Your Mandal
              </Button>
            ) : (
              <Button
                component={Link}
                to="/register"
                variant="outlined"
                size="large"
              >
                Get Started
              </Button>
            )}
          </Box>
        </Container>
      </Paper>
    </Box>
  );
};

export default Home;
