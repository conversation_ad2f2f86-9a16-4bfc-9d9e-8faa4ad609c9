import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
} from '@mui/material';
import {
  Save as SaveIcon,
  CheckCircle as CompleteIcon,
  Settings as DetailsIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { mandalAPI } from '../../utils/api';
import toast from 'react-hot-toast';

interface GaneshDetailsFormData {
  ganeshDetails: {
    murtiType: string;
    height: string;
    weight: string;
    murtikarName?: string;
    festivalDuration: string;
    decorationType: string;
    theme: string;
    description: string;
    specialFeatures: string[];
  };
}

const MandalGaneshDetails: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [saved, setSaved] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<GaneshDetailsFormData>({
    defaultValues: {
      ganeshDetails: {
        murtiType: '',
        height: '',
        weight: '',
        murtikarName: '',
        festivalDuration: '1.5',
        decorationType: '',
        theme: '',
        description: '',
        specialFeatures: [],
      },
    },
  });

  const specialFeatures = watch('ganeshDetails.specialFeatures') || [];

  const addSpecialFeature = (feature: string) => {
    if (feature && !specialFeatures.includes(feature)) {
      setValue('ganeshDetails.specialFeatures', [...specialFeatures, feature]);
    }
  };

  const removeSpecialFeature = (feature: string) => {
    setValue('ganeshDetails.specialFeatures', specialFeatures.filter((f: string) => f !== feature));
  };

  const onSubmit = async (data: GaneshDetailsFormData) => {
    try {
      setLoading(true);
      await mandalAPI.saveDraft(data);
      toast.success('Ganesh details saved successfully!');
      setSaved(true);
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to save Ganesh details');
    } finally {
      setLoading(false);
    }
  };

  const isFormValid = () => {
    const formData = watch();
    return (
      formData.ganeshDetails.murtiType &&
      formData.ganeshDetails.height &&
      formData.ganeshDetails.weight &&
      formData.ganeshDetails.decorationType &&
      formData.ganeshDetails.theme &&
      formData.ganeshDetails.description
    );
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
        <DetailsIcon color="primary" />
        <Typography variant="h5" color="primary">
          Current Year Ganesha Details
        </Typography>
        {saved && <CompleteIcon color="success" />}
      </Box>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Provide detailed information about your current year Ganesha murti and celebration.
      </Typography>

      {saved && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Ganesh details have been saved. You can proceed to add photos.
        </Alert>
      )}

      <Card>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Grid container spacing={3}>
              {/* Murti Details */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Murti Information
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="ganeshDetails.murtiType"
                  control={control}
                  rules={{ required: 'Murti type is required' }}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!errors.ganeshDetails?.murtiType}>
                      <InputLabel>Murti Type</InputLabel>
                      <Select {...field} label="Murti Type">
                        <MenuItem value="clay">Clay (Eco-friendly)</MenuItem>
                        <MenuItem value="plaster_of_paris">Plaster of Paris</MenuItem>
                        <MenuItem value="eco_friendly">Eco-friendly Material</MenuItem>
                        <MenuItem value="fiber">Fiber</MenuItem>
                        <MenuItem value="metal">Metal</MenuItem>
                        <MenuItem value="stone">Stone</MenuItem>
                        <MenuItem value="other">Other</MenuItem>
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="ganeshDetails.festivalDuration"
                  control={control}
                  rules={{ required: 'Festival duration is required' }}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!errors.ganeshDetails?.festivalDuration}>
                      <InputLabel>Festival Duration</InputLabel>
                      <Select {...field} label="Festival Duration">
                        <MenuItem value="1.5">1½ Days</MenuItem>
                        <MenuItem value="3">3 Days</MenuItem>
                        <MenuItem value="5">5 Days</MenuItem>
                        <MenuItem value="7">7 Days</MenuItem>
                        <MenuItem value="10">10 Days</MenuItem>
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="ganeshDetails.height"
                  control={control}
                  rules={{ required: 'Height is required' }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Murti Height"
                      placeholder="e.g., 5 feet, 10 feet"
                      error={!!errors.ganeshDetails?.height}
                      helperText={errors.ganeshDetails?.height?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="ganeshDetails.weight"
                  control={control}
                  rules={{ required: 'Weight is required' }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Murti Weight"
                      placeholder="e.g., 50 kg, 100 kg"
                      error={!!errors.ganeshDetails?.weight}
                      helperText={errors.ganeshDetails?.weight?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="ganeshDetails.murtikarName"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Murtikar Name (Optional)"
                      placeholder="Name of the sculptor/artist"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="ganeshDetails.decorationType"
                  control={control}
                  rules={{ required: 'Decoration type is required' }}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!errors.ganeshDetails?.decorationType}>
                      <InputLabel>Decoration Type</InputLabel>
                      <Select {...field} label="Decoration Type">
                        <MenuItem value="traditional">Traditional</MenuItem>
                        <MenuItem value="modern">Modern</MenuItem>
                        <MenuItem value="theme_based">Theme Based</MenuItem>
                        <MenuItem value="eco_friendly">Eco-friendly</MenuItem>
                        <MenuItem value="cultural">Cultural</MenuItem>
                        <MenuItem value="mythological">Mythological</MenuItem>
                        <MenuItem value="other">Other</MenuItem>
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="ganeshDetails.theme"
                  control={control}
                  rules={{ required: 'Theme is required' }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Theme"
                      placeholder="e.g., Eco-friendly, Traditional, Modern Art"
                      error={!!errors.ganeshDetails?.theme}
                      helperText={errors.ganeshDetails?.theme?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="ganeshDetails.description"
                  control={control}
                  rules={{ 
                    required: 'Description is required',
                    maxLength: { value: 2000, message: 'Description cannot exceed 2000 characters' }
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      multiline
                      rows={4}
                      label="Description"
                      placeholder="Describe your Ganesha, decoration, special features, and what makes it unique..."
                      error={!!errors.ganeshDetails?.description}
                      helperText={errors.ganeshDetails?.description?.message}
                    />
                  )}
                />
              </Grid>

              {/* Special Features */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                  Special Features
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  {specialFeatures.map((feature: string) => (
                    <Chip
                      key={feature}
                      label={feature}
                      onDelete={() => removeSpecialFeature(feature)}
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Box>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {['Live Darshan', 'Cultural Programs', 'Food Distribution', 'Medical Camp', 'Blood Donation', 'Environmental Initiative', 'Aarti', 'Bhajan', 'Dance Performance'].map((feature) => (
                    <Button
                      key={feature}
                      size="small"
                      variant="outlined"
                      onClick={() => addSpecialFeature(feature)}
                      disabled={specialFeatures.includes(feature)}
                    >
                      {feature}
                    </Button>
                  ))}
                </Box>
              </Grid>

              {/* Action Buttons */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                  <Button
                    type="submit"
                    variant="contained"
                    startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                    disabled={loading}
                    size="large"
                  >
                    {loading ? 'Saving...' : 'Save Ganesh Details'}
                  </Button>

                  {isFormValid() && (
                    <Button
                      variant="outlined"
                      onClick={() => window.location.href = '/user-dashboard/mandal/photos'}
                      size="large"
                    >
                      Next: Photo Gallery
                    </Button>
                  )}
                </Box>
              </Grid>
            </Grid>
          </form>
        </CardContent>
      </Card>
    </Box>
  );
};

export default MandalGaneshDetails;
