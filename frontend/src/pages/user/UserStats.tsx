import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Chip,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  CheckCircle as CompleteIcon,
  Warning as WarningIcon,
  Add as AddIcon,
  Timeline as ProgressIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const UserStats: React.FC = () => {
  const navigate = useNavigate();

  // Mock data - replace with actual data from API
  const stats = {
    totalMandals: 1,
    completedSections: 2,
    totalSections: 5,
    currentYear: 2025,
    yearsManaged: ['2024', '2025'],
    pendingApproval: true,
  };

  const completionPercentage = (stats.completedSections / stats.totalSections) * 100;

  const nextSteps = [
    { text: 'Complete Team Members', path: '/user-dashboard/mandal/team-members' },
    { text: 'Add Ganesh Details', path: '/user-dashboard/mandal/ganesh-details' },
    { text: 'Upload Photos', path: '/user-dashboard/mandal/photos' },
  ];

  return (
    <Box>
      <Typography variant="h4" gutterBottom color="primary">
        🕉️ Welcome to Your Dashboard
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Manage your mandal information and track your progress
      </Typography>

      <Grid container spacing={3}>
        {/* Progress Overview */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                <ProgressIcon color="primary" />
                <Typography variant="h6">Registration Progress</Typography>
              </Box>
              
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">
                    {stats.completedSections} of {stats.totalSections} sections completed
                  </Typography>
                  <Typography variant="body2" color="primary">
                    {Math.round(completionPercentage)}%
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={completionPercentage} 
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>

              {completionPercentage < 100 ? (
                <Alert severity="warning" sx={{ mb: 2 }}>
                  Complete all required sections to submit for approval
                </Alert>
              ) : (
                <Alert severity="success" sx={{ mb: 2 }}>
                  All sections completed! Ready for submission
                </Alert>
              )}

              <Typography variant="subtitle2" gutterBottom>
                Next Steps:
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {nextSteps.map((step, index) => (
                  <Button
                    key={index}
                    variant="outlined"
                    startIcon={<AddIcon />}
                    onClick={() => navigate(step.path)}
                    sx={{ justifyContent: 'flex-start' }}
                  >
                    {step.text}
                  </Button>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Stats */}
        <Grid item xs={12} md={4}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <DashboardIcon color="primary" />
                    <Box>
                      <Typography variant="h4" color="primary">
                        {stats.totalMandals}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Mandal Registered
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <CompleteIcon color="success" />
                    <Box>
                      <Typography variant="h4" color="success.main">
                        {stats.completedSections}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Sections Complete
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <WarningIcon color="warning" />
                    <Box>
                      <Typography variant="h4" color="warning.main">
                        {stats.totalSections - stats.completedSections}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Sections Pending
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>

        {/* Year Management */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Year Management
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Manage your mandal details for different years
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                {stats.yearsManaged.map((year) => (
                  <Chip
                    key={year}
                    label={year}
                    color={year === stats.currentYear.toString() ? 'primary' : 'default'}
                    variant={year === stats.currentYear.toString() ? 'filled' : 'outlined'}
                  />
                ))}
              </Box>

              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={() => navigate('/user-dashboard/mandal/years')}
              >
                Manage Years
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Status Alert */}
        <Grid item xs={12}>
          {stats.pendingApproval ? (
            <Alert severity="info">
              <Typography variant="subtitle2" gutterBottom>
                Approval Status: Pending
              </Typography>
              <Typography variant="body2">
                Your mandal registration is under review. You'll be notified once it's approved.
              </Typography>
            </Alert>
          ) : (
            <Alert severity="success">
              <Typography variant="subtitle2" gutterBottom>
                Approval Status: Approved
              </Typography>
              <Typography variant="body2">
                Your mandal is approved and visible to visitors!
              </Typography>
            </Alert>
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default UserStats;
