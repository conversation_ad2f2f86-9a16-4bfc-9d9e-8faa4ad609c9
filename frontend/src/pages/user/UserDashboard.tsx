import React, { useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Info as InfoIcon,
  Contacts as ContactIcon,
  Group as TeamIcon,
  Settings as DetailsIcon,
  CheckCircle as CompleteIcon,
  RadioButtonUnchecked as IncompleteIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

const UserDashboard: React.FC = () => {
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);

  const steps = [
    { id: 1, title: 'Basic Information', icon: <InfoIcon />, completed: false },
    { id: 2, title: 'Contact Details', icon: <ContactIcon />, completed: false },
    { id: 3, title: 'Team Members', icon: <TeamIcon />, completed: false },
    { id: 4, title: '<PERSON><PERSON><PERSON> Details', icon: <DetailsIcon />, completed: false },
  ];

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <BasicInfoForm onNext={() => setCurrentStep(2)} />;
      case 2:
        return <ContactInfoForm onNext={() => setCurrentStep(3)} onBack={() => setCurrentStep(1)} />;
      case 3:
        return <TeamMembersForm onNext={() => setCurrentStep(4)} onBack={() => setCurrentStep(2)} />;
      case 4:
        return <GaneshDetailsForm onBack={() => setCurrentStep(3)} />;
      default:
        return <BasicInfoForm onNext={() => setCurrentStep(2)} />;
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom color="primary">
        🕉️ Mandal Registration Dashboard
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Welcome {user?.firstName}! Complete your mandal registration step by step.
      </Typography>

      <Grid container spacing={4}>
        {/* Sidebar */}
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Registration Steps
            </Typography>
            <List>
              {steps.map((step) => (
                <ListItem
                  key={step.id}
                  button
                  onClick={() => setCurrentStep(step.id)}
                  sx={{
                    bgcolor: currentStep === step.id ? 'primary.light' : 'transparent',
                    borderRadius: 1,
                    mb: 1
                  }}
                >
                  <ListItemIcon>
                    {step.completed ? <CompleteIcon color="success" /> : step.icon}
                  </ListItemIcon>
                  <ListItemText
                    primary={step.title}
                    secondary={`Step ${step.id}`}
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Main Content */}
        <Grid item xs={12} md={9}>
          <Paper sx={{ p: 3 }}>
            {renderStepContent()}
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};
};

// Simple Form Components
const BasicInfoForm: React.FC<{ onNext: () => void }> = ({ onNext }) => {
  const [formData, setFormData] = useState({
    name: '',
    registrationNumber: '',
    establishmentYear: new Date().getFullYear(),
    city: '',
    state: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.name && formData.city) {
      try {
        // Simple API call
        const response = await fetch('http://localhost:5001/api/mandals/draft', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            name: formData.name,
            registrationNumber: formData.registrationNumber,
            establishmentYear: formData.establishmentYear,
            location: {
              city: formData.city,
              state: formData.state
            }
          })
        });

        if (response.ok) {
          alert('Basic information saved!');
          onNext();
        } else {
          alert('Error saving data');
        }
      } catch (error) {
        console.error('Error:', error);
        alert('Error saving data');
      }
    } else {
      alert('Please fill required fields');
    }
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Step 1: Basic Information
      </Typography>
      <form onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <input
              type="text"
              placeholder="Mandal Name *"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              style={{ width: '100%', padding: '10px', marginBottom: '10px' }}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <input
              type="text"
              placeholder="Registration Number"
              value={formData.registrationNumber}
              onChange={(e) => setFormData({...formData, registrationNumber: e.target.value})}
              style={{ width: '100%', padding: '10px', marginBottom: '10px' }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <input
              type="number"
              placeholder="Establishment Year"
              value={formData.establishmentYear}
              onChange={(e) => setFormData({...formData, establishmentYear: parseInt(e.target.value)})}
              style={{ width: '100%', padding: '10px', marginBottom: '10px' }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <input
              type="text"
              placeholder="City *"
              value={formData.city}
              onChange={(e) => setFormData({...formData, city: e.target.value})}
              style={{ width: '100%', padding: '10px', marginBottom: '10px' }}
              required
            />
          </Grid>
          <Grid item xs={12}>
            <input
              type="text"
              placeholder="State *"
              value={formData.state}
              onChange={(e) => setFormData({...formData, state: e.target.value})}
              style={{ width: '100%', padding: '10px', marginBottom: '10px' }}
              required
            />
          </Grid>
          <Grid item xs={12}>
            <Button type="submit" variant="contained" size="large">
              Save & Next
            </Button>
          </Grid>
        </Grid>
      </form>
    </Box>
  );
};

const ContactInfoForm: React.FC<{ onNext: () => void; onBack: () => void }> = ({ onNext, onBack }) => {
  const [formData, setFormData] = useState({
    email: '',
    phone: '',
    website: '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.email && formData.phone) {
      alert('Contact information saved!');
      onNext();
    } else {
      alert('Please fill required fields');
    }
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Step 2: Contact Information
      </Typography>
      <form onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <input
              type="email"
              placeholder="Email Address *"
              value={formData.email}
              onChange={(e) => setFormData({...formData, email: e.target.value})}
              style={{ width: '100%', padding: '10px', marginBottom: '10px' }}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <input
              type="tel"
              placeholder="Phone Number *"
              value={formData.phone}
              onChange={(e) => setFormData({...formData, phone: e.target.value})}
              style={{ width: '100%', padding: '10px', marginBottom: '10px' }}
              required
            />
          </Grid>
          <Grid item xs={12}>
            <input
              type="url"
              placeholder="Website (Optional)"
              value={formData.website}
              onChange={(e) => setFormData({...formData, website: e.target.value})}
              style={{ width: '100%', padding: '10px', marginBottom: '10px' }}
            />
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button onClick={onBack} variant="outlined">
                Back
              </Button>
              <Button type="submit" variant="contained" size="large">
                Save & Next
              </Button>
            </Box>
          </Grid>
        </Grid>
      </form>
    </Box>
  );
};

const TeamMembersForm: React.FC<{ onNext: () => void; onBack: () => void }> = ({ onNext, onBack }) => {
  const [members, setMembers] = useState([{ name: '', designation: '', phone: '' }]);

  const addMember = () => {
    setMembers([...members, { name: '', designation: '', phone: '' }]);
  };

  const updateMember = (index: number, field: string, value: string) => {
    const updated = [...members];
    updated[index] = { ...updated[index], [field]: value };
    setMembers(updated);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (members[0].name && members[0].designation) {
      alert('Team members saved!');
      onNext();
    } else {
      alert('Please add at least one team member');
    }
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Step 3: Team Members
      </Typography>
      <form onSubmit={handleSubmit}>
        {members.map((member, index) => (
          <Card key={index} sx={{ mb: 2, p: 2 }}>
            <Typography variant="h6">Member {index + 1}</Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <input
                  type="text"
                  placeholder="Full Name *"
                  value={member.name}
                  onChange={(e) => updateMember(index, 'name', e.target.value)}
                  style={{ width: '100%', padding: '10px', marginBottom: '10px' }}
                  required
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <select
                  value={member.designation}
                  onChange={(e) => updateMember(index, 'designation', e.target.value)}
                  style={{ width: '100%', padding: '10px', marginBottom: '10px' }}
                  required
                >
                  <option value="">Select Designation *</option>
                  <option value="President">President</option>
                  <option value="Secretary">Secretary</option>
                  <option value="Treasurer">Treasurer</option>
                  <option value="Committee Member">Committee Member</option>
                  <option value="Volunteer">Volunteer</option>
                </select>
              </Grid>
              <Grid item xs={12} md={4}>
                <input
                  type="tel"
                  placeholder="Phone Number"
                  value={member.phone}
                  onChange={(e) => updateMember(index, 'phone', e.target.value)}
                  style={{ width: '100%', padding: '10px', marginBottom: '10px' }}
                />
              </Grid>
            </Grid>
          </Card>
        ))}
        <Button onClick={addMember} variant="outlined" sx={{ mb: 2 }}>
          Add Another Member
        </Button>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button onClick={onBack} variant="outlined">
            Back
          </Button>
          <Button type="submit" variant="contained" size="large">
            Save & Next
          </Button>
        </Box>
      </form>
    </Box>
  );
};

const GaneshDetailsForm: React.FC<{ onBack: () => void }> = ({ onBack }) => {
  const [formData, setFormData] = useState({
    height: '',
    weight: '',
    theme: '',
    duration: '1.5',
    murtikarName: '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.height && formData.theme) {
      alert('Ganesh details saved! Registration complete!');
    } else {
      alert('Please fill required fields');
    }
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Step 4: Ganesh Details
      </Typography>
      <form onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <input
              type="text"
              placeholder="Murti Height *"
              value={formData.height}
              onChange={(e) => setFormData({...formData, height: e.target.value})}
              style={{ width: '100%', padding: '10px', marginBottom: '10px' }}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <input
              type="text"
              placeholder="Murti Weight"
              value={formData.weight}
              onChange={(e) => setFormData({...formData, weight: e.target.value})}
              style={{ width: '100%', padding: '10px', marginBottom: '10px' }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <select
              value={formData.duration}
              onChange={(e) => setFormData({...formData, duration: e.target.value})}
              style={{ width: '100%', padding: '10px', marginBottom: '10px' }}
            >
              <option value="1.5">1½ Days</option>
              <option value="3">3 Days</option>
              <option value="5">5 Days</option>
              <option value="7">7 Days</option>
              <option value="10">10 Days</option>
            </select>
          </Grid>
          <Grid item xs={12} md={6}>
            <input
              type="text"
              placeholder="Murtikar Name (Optional)"
              value={formData.murtikarName}
              onChange={(e) => setFormData({...formData, murtikarName: e.target.value})}
              style={{ width: '100%', padding: '10px', marginBottom: '10px' }}
            />
          </Grid>
          <Grid item xs={12}>
            <input
              type="text"
              placeholder="Theme *"
              value={formData.theme}
              onChange={(e) => setFormData({...formData, theme: e.target.value})}
              style={{ width: '100%', padding: '10px', marginBottom: '10px' }}
              required
            />
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button onClick={onBack} variant="outlined">
                Back
              </Button>
              <Button type="submit" variant="contained" size="large">
                Complete Registration
              </Button>
            </Box>
          </Grid>
        </Grid>
      </form>
    </Box>
  );
};

export default UserDashboard;
