import React, { useState, useEffect } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Container,
  Paper,
  IconButton,
  useTheme,
  useMediaQuery,
  Chip,
  Alert,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  AccountBox as ProfileIcon,
  Temple as TempleIcon,
  Add as AddIcon,
  Info as InfoIcon,
  Contacts as ContactIcon,
  Group as TeamIcon,
  Settings as DetailsIcon,
  Photo as PhotoIcon,
  CalendarToday as YearIcon,
  CheckCircle as CompleteIcon,
  RadioButtonUnchecked as IncompleteIcon,
} from '@mui/icons-material';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import UserProfile from './UserProfile';
import MandalBasicInfo from './MandalBasicInfo';
import MandalContactInfo from './MandalContactInfo';
import MandalTeamMembers from './MandalTeamMembers';
import MandalGaneshDetails from './MandalGaneshDetails';
import MandalPhotos from './MandalPhotos';
import MandalYearManagement from './MandalYearManagement';
import UserStats from './UserStats';

const drawerWidth = 280;

interface SidebarItem {
  text: string;
  icon: React.ReactElement;
  path: string;
  completed?: boolean;
  required?: boolean;
}

const UserDashboard: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();

  const [completionStatus, setCompletionStatus] = useState({
    basicInfo: false,
    contactInfo: false,
    teamMembers: false,
    ganeshDetails: false,
    photos: false,
  });

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const menuItems: SidebarItem[] = [
    {
      text: 'Dashboard',
      icon: <DashboardIcon />,
      path: '/user-dashboard',
      completed: true,
    },
    {
      text: 'My Profile',
      icon: <ProfileIcon />,
      path: '/user-dashboard/profile',
      completed: true,
    },
  ];

  const mandalSections: SidebarItem[] = [
    {
      text: 'Basic Information',
      icon: <InfoIcon />,
      path: '/user-dashboard/mandal/basic-info',
      completed: completionStatus.basicInfo,
      required: true,
    },
    {
      text: 'Contact Details',
      icon: <ContactIcon />,
      path: '/user-dashboard/mandal/contact-info',
      completed: completionStatus.contactInfo,
      required: true,
    },
    {
      text: 'Team Members',
      icon: <TeamIcon />,
      path: '/user-dashboard/mandal/team-members',
      completed: completionStatus.teamMembers,
      required: true,
    },
    {
      text: 'Ganesh Details',
      icon: <DetailsIcon />,
      path: '/user-dashboard/mandal/ganesh-details',
      completed: completionStatus.ganeshDetails,
      required: true,
    },
    {
      text: 'Photo Gallery',
      icon: <PhotoIcon />,
      path: '/user-dashboard/mandal/photos',
      completed: completionStatus.photos,
      required: false,
    },
    {
      text: 'Year Management',
      icon: <YearIcon />,
      path: '/user-dashboard/mandal/years',
      completed: true,
      required: false,
    },
  ];

  const drawer = (
    <Box>
      <Toolbar>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <TempleIcon sx={{ color: 'primary.main', fontSize: 32 }} />
          <Typography variant="h6" noWrap component="div" color="primary">
            🕉️ Ganesh Darshan
          </Typography>
        </Box>
      </Toolbar>
      <Divider />

      {/* User Info */}
      <Box sx={{ p: 2, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
        <Typography variant="subtitle1" fontWeight="bold">
          Welcome, {user?.firstName}!
        </Typography>
        <Typography variant="body2" sx={{ opacity: 0.9 }}>
          {user?.email}
        </Typography>
      </Box>

      {/* Main Navigation */}
      <List>
        {menuItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              selected={location.pathname === item.path}
              onClick={() => navigate(item.path)}
            >
              <ListItemIcon>{item.icon}</ListItemIcon>
              <ListItemText primary={item.text} />
              {item.completed && (
                <CompleteIcon sx={{ color: 'success.main', fontSize: 20 }} />
              )}
            </ListItemButton>
          </ListItem>
        ))}
      </List>

      <Divider sx={{ my: 1 }} />

      {/* Mandal Management Section */}
      <Box sx={{ px: 2, py: 1 }}>
        <Typography variant="subtitle2" color="text.secondary" fontWeight="bold">
          MANDAL MANAGEMENT
        </Typography>
      </Box>

      <List>
        {mandalSections.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              selected={location.pathname === item.path}
              onClick={() => navigate(item.path)}
              sx={{
                pl: 3,
                borderLeft: item.required ? '3px solid' : 'none',
                borderLeftColor: item.completed ? 'success.main' : 'warning.main',
              }}
            >
              <ListItemIcon>
                {item.icon}
              </ListItemIcon>
              <ListItemText 
                primary={item.text}
                secondary={item.required ? 'Required' : 'Optional'}
              />
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {item.required && (
                  <Chip
                    size="small"
                    label={item.completed ? 'Done' : 'Pending'}
                    color={item.completed ? 'success' : 'warning'}
                    variant="outlined"
                  />
                )}
                {item.completed ? (
                  <CompleteIcon sx={{ color: 'success.main', fontSize: 20 }} />
                ) : (
                  <IncompleteIcon sx={{ color: 'text.secondary', fontSize: 20 }} />
                )}
              </Box>
            </ListItemButton>
          </ListItem>
        ))}
      </List>

      <Divider sx={{ my: 2 }} />

      {/* Progress Summary */}
      <Box sx={{ p: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          Completion Progress
        </Typography>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          {Object.entries(completionStatus).map(([key, completed]) => (
            <Box key={key} sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="caption" sx={{ textTransform: 'capitalize' }}>
                {key.replace(/([A-Z])/g, ' $1').trim()}
              </Typography>
              {completed ? (
                <CompleteIcon sx={{ color: 'success.main', fontSize: 16 }} />
              ) : (
                <IncompleteIcon sx={{ color: 'text.secondary', fontSize: 16 }} />
              )}
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar
        position="fixed"
        sx={{
          width: { md: `calc(100% - ${drawerWidth}px)` },
          ml: { md: `${drawerWidth}px` },
          bgcolor: 'background.paper',
          color: 'text.primary',
          boxShadow: 1,
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div">
            User Dashboard
          </Typography>
        </Toolbar>
      </AppBar>

      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mt: 8,
        }}
      >
        <Routes>
          <Route path="/" element={<UserStats />} />
          <Route path="/profile" element={<UserProfile />} />
          <Route path="/mandal/basic-info" element={<MandalBasicInfo />} />
          <Route path="/mandal/contact-info" element={<MandalContactInfo />} />
          <Route path="/mandal/team-members" element={<MandalTeamMembers />} />
          <Route path="/mandal/ganesh-details" element={<MandalGaneshDetails />} />
          <Route path="/mandal/photos" element={<MandalPhotos />} />
          <Route path="/mandal/years" element={<MandalYearManagement />} />
        </Routes>
      </Box>
    </Box>
  );
};

export default UserDashboard;
