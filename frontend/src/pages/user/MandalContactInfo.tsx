import React, { useState } from 'react';
import {
  Card,
  Card<PERSON>ontent,
  <PERSON>pography,
  TextField,
  Button,
  Grid,
  Box,
  Alert,
  CircularProgress,
  InputAdornment,
} from '@mui/material';
import {
  Save as SaveIcon,
  CheckCircle as CompleteIcon,
  Contacts as ContactIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Language as WebsiteIcon,
  Facebook,
  Instagram,
  Twitter,
  YouTube,
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { mandalAPI } from '../../utils/api';
import toast from 'react-hot-toast';

interface ContactInfoFormData {
  contactInfo: {
    email: string;
    phone: string;
    alternatePhone?: string;
    website?: string;
    socialMedia: {
      facebook?: string;
      instagram?: string;
      twitter?: string;
      youtube?: string;
    };
  };
}

const MandalContactInfo: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [saved, setSaved] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<ContactInfoFormData>({
    defaultValues: {
      contactInfo: {
        email: '',
        phone: '',
        alternatePhone: '',
        website: '',
        socialMedia: {
          facebook: '',
          instagram: '',
          twitter: '',
          youtube: '',
        },
      },
    },
  });

  const onSubmit = async (data: ContactInfoFormData) => {
    try {
      setLoading(true);
      await mandalAPI.saveDraft(data);
      toast.success('Contact information saved successfully!');
      setSaved(true);
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to save contact information');
    } finally {
      setLoading(false);
    }
  };

  const isFormValid = () => {
    const formData = watch();
    return formData.contactInfo.email && formData.contactInfo.phone;
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
        <ContactIcon color="primary" />
        <Typography variant="h5" color="primary">
          Contact Information
        </Typography>
        {saved && <CompleteIcon color="success" />}
      </Box>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Provide contact details so visitors can reach you. This information will be displayed publicly.
      </Typography>

      {saved && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Contact information has been saved. You can proceed to the next section.
        </Alert>
      )}

      <Card>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Grid container spacing={3}>
              {/* Primary Contact */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Primary Contact Details
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="contactInfo.email"
                  control={control}
                  rules={{ 
                    required: 'Email is required',
                    pattern: { value: /^\S+@\S+$/i, message: 'Invalid email' }
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      type="email"
                      label="Email Address"
                      error={!!errors.contactInfo?.email}
                      helperText={errors.contactInfo?.email?.message}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <EmailIcon color="primary" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="contactInfo.phone"
                  control={control}
                  rules={{ 
                    required: 'Phone number is required',
                    pattern: { value: /^[6-9]\d{9}$/, message: 'Invalid phone number' }
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Primary Phone Number"
                      error={!!errors.contactInfo?.phone}
                      helperText={errors.contactInfo?.phone?.message}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PhoneIcon color="primary" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="contactInfo.alternatePhone"
                  control={control}
                  rules={{ 
                    pattern: { value: /^[6-9]\d{9}$/, message: 'Invalid phone number' }
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Alternate Phone Number (Optional)"
                      error={!!errors.contactInfo?.alternatePhone}
                      helperText={errors.contactInfo?.alternatePhone?.message}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PhoneIcon color="secondary" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="contactInfo.website"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Website (Optional)"
                      placeholder="https://your-website.com"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <WebsiteIcon color="primary" />
                          </InputAdornment>
                        ),
                      }}
                    />
                  )}
                />
              </Grid>

              {/* Social Media */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                  Social Media Links (Optional)
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="contactInfo.socialMedia.facebook"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Facebook Page"
                      placeholder="https://facebook.com/your-page"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Facebook sx={{ color: '#1877F2' }} />
                          </InputAdornment>
                        ),
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="contactInfo.socialMedia.instagram"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Instagram Profile"
                      placeholder="https://instagram.com/your-profile"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Instagram sx={{ color: '#E4405F' }} />
                          </InputAdornment>
                        ),
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="contactInfo.socialMedia.twitter"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Twitter Profile"
                      placeholder="https://twitter.com/your-profile"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Twitter sx={{ color: '#1DA1F2' }} />
                          </InputAdornment>
                        ),
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="contactInfo.socialMedia.youtube"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="YouTube Channel"
                      placeholder="https://youtube.com/your-channel"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <YouTube sx={{ color: '#FF0000' }} />
                          </InputAdornment>
                        ),
                      }}
                    />
                  )}
                />
              </Grid>

              {/* Action Buttons */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                  <Button
                    type="submit"
                    variant="contained"
                    startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                    disabled={loading}
                    size="large"
                  >
                    {loading ? 'Saving...' : 'Save Contact Information'}
                  </Button>

                  {isFormValid() && (
                    <Button
                      variant="outlined"
                      onClick={() => window.location.href = '/user-dashboard/mandal/team-members'}
                      size="large"
                    >
                      Next: Team Members
                    </Button>
                  )}
                </Box>
              </Grid>
            </Grid>
          </form>
        </CardContent>
      </Card>
    </Box>
  );
};

export default MandalContactInfo;
