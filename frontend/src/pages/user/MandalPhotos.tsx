import React from 'react';
import { Typography, Box, Card, CardContent, Alert } from '@mui/material';
import { Photo as PhotoIcon } from '@mui/icons-material';

const MandalPhotos: React.FC = () => {
  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
        <PhotoIcon color="primary" />
        <Typography variant="h5" color="primary">
          Photo Gallery
        </Typography>
      </Box>

      <Card>
        <CardContent>
          <Alert severity="info">
            Photo gallery functionality coming soon...
          </Alert>
        </CardContent>
      </Card>
    </Box>
  );
};

export default MandalPhotos;
