import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Ty<PERSON>graphy,
  TextField,
  Button,
  Grid,
  Box,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Avatar,
  Paper,
} from '@mui/material';
import {
  Save as SaveIcon,
  CheckCircle as CompleteIcon,
  Group as TeamIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  CloudUpload as UploadIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import { mandalAPI } from '../../utils/api';
import toast from 'react-hot-toast';

interface TeamMember {
  name: string;
  designation: string;
  phone?: string;
  photo?: string;
}

interface TeamMembersFormData {
  teamMembers: TeamMember[];
}

const MandalTeamMembers: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [saved, setSaved] = useState(false);
  const [uploadingPhoto, setUploadingPhoto] = useState<number | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<TeamMembersFormData>({
    defaultValues: {
      teamMembers: [{ name: '', designation: '', phone: '', photo: '' }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'teamMembers',
  });

  const onSubmit = async (data: TeamMembersFormData) => {
    try {
      setLoading(true);
      await mandalAPI.saveDraft(data);
      toast.success('Team members saved successfully!');
      setSaved(true);
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to save team members');
    } finally {
      setLoading(false);
    }
  };

  const handlePhotoUpload = async (file: File, index: number) => {
    try {
      setUploadingPhoto(index);
      
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('photo', file);
      
      // Mock upload - replace with actual upload API
      // const response = await uploadAPI.uploadPhoto(formData);
      // setValue(`teamMembers.${index}.photo`, response.data.url);
      
      // For now, create a local URL
      const photoUrl = URL.createObjectURL(file);
      setValue(`teamMembers.${index}.photo`, photoUrl);
      
      toast.success('Photo uploaded successfully!');
    } catch (error) {
      toast.error('Failed to upload photo');
    } finally {
      setUploadingPhoto(null);
    }
  };

  const isFormValid = () => {
    const formData = watch();
    return formData.teamMembers.some(member => member.name && member.designation);
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
        <TeamIcon color="primary" />
        <Typography variant="h5" color="primary">
          Team Members
        </Typography>
        {saved && <CompleteIcon color="success" />}
      </Box>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Add your mandal team members with their designations and photos. This helps visitors know who to contact.
      </Typography>

      {saved && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Team members have been saved. You can proceed to the next section.
        </Alert>
      )}

      <Card>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Grid container spacing={3}>
              {fields.map((field, index) => (
                <Grid item xs={12} key={field.id}>
                  <Paper sx={{ p: 3, border: '1px solid', borderColor: 'divider' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h6">
                        Team Member {index + 1}
                      </Typography>
                      {fields.length > 1 && (
                        <IconButton
                          onClick={() => remove(index)}
                          color="error"
                          size="small"
                        >
                          <DeleteIcon />
                        </IconButton>
                      )}
                    </Box>

                    <Grid container spacing={2}>
                      {/* Photo Upload */}
                      <Grid item xs={12} md={3}>
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
                          <Avatar
                            src={watch(`teamMembers.${index}.photo`)}
                            sx={{ width: 100, height: 100 }}
                          >
                            <PersonIcon sx={{ fontSize: 40 }} />
                          </Avatar>
                          
                          <input
                            accept="image/*"
                            style={{ display: 'none' }}
                            id={`photo-upload-${index}`}
                            type="file"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) {
                                handlePhotoUpload(file, index);
                              }
                            }}
                          />
                          <label htmlFor={`photo-upload-${index}`}>
                            <Button
                              variant="outlined"
                              component="span"
                              startIcon={uploadingPhoto === index ? <CircularProgress size={16} /> : <UploadIcon />}
                              disabled={uploadingPhoto === index}
                              size="small"
                            >
                              {uploadingPhoto === index ? 'Uploading...' : 'Upload Photo'}
                            </Button>
                          </label>
                        </Box>
                      </Grid>

                      {/* Member Details */}
                      <Grid item xs={12} md={9}>
                        <Grid container spacing={2}>
                          <Grid item xs={12} md={6}>
                            <Controller
                              name={`teamMembers.${index}.name`}
                              control={control}
                              rules={{ required: 'Name is required' }}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  fullWidth
                                  label="Full Name"
                                  error={!!errors.teamMembers?.[index]?.name}
                                  helperText={errors.teamMembers?.[index]?.name?.message}
                                />
                              )}
                            />
                          </Grid>

                          <Grid item xs={12} md={6}>
                            <Controller
                              name={`teamMembers.${index}.designation`}
                              control={control}
                              rules={{ required: 'Designation is required' }}
                              render={({ field }) => (
                                <FormControl fullWidth error={!!errors.teamMembers?.[index]?.designation}>
                                  <InputLabel>Designation</InputLabel>
                                  <Select {...field} label="Designation">
                                    <MenuItem value="President">President</MenuItem>
                                    <MenuItem value="Vice President">Vice President</MenuItem>
                                    <MenuItem value="Secretary">Secretary</MenuItem>
                                    <MenuItem value="Treasurer">Treasurer</MenuItem>
                                    <MenuItem value="Joint Secretary">Joint Secretary</MenuItem>
                                    <MenuItem value="Committee Member">Committee Member</MenuItem>
                                    <MenuItem value="Coordinator">Coordinator</MenuItem>
                                    <MenuItem value="Volunteer">Volunteer</MenuItem>
                                    <MenuItem value="Cultural Head">Cultural Head</MenuItem>
                                    <MenuItem value="Decoration Head">Decoration Head</MenuItem>
                                    <MenuItem value="Security Head">Security Head</MenuItem>
                                    <MenuItem value="Other">Other</MenuItem>
                                  </Select>
                                </FormControl>
                              )}
                            />
                          </Grid>

                          <Grid item xs={12}>
                            <Controller
                              name={`teamMembers.${index}.phone`}
                              control={control}
                              rules={{ 
                                pattern: { value: /^[6-9]\d{9}$/, message: 'Invalid phone number' }
                              }}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  fullWidth
                                  label="Phone Number (Optional)"
                                  error={!!errors.teamMembers?.[index]?.phone}
                                  helperText={errors.teamMembers?.[index]?.phone?.message}
                                  placeholder="Contact number for this member"
                                />
                              )}
                            />
                          </Grid>
                        </Grid>
                      </Grid>
                    </Grid>
                  </Paper>
                </Grid>
              ))}

              {/* Add Member Button */}
              <Grid item xs={12}>
                <Button
                  onClick={() => append({ name: '', designation: '', phone: '', photo: '' })}
                  startIcon={<AddIcon />}
                  variant="outlined"
                  fullWidth
                  sx={{ py: 2 }}
                >
                  Add Another Team Member
                </Button>
              </Grid>

              {/* Action Buttons */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                  <Button
                    type="submit"
                    variant="contained"
                    startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                    disabled={loading}
                    size="large"
                  >
                    {loading ? 'Saving...' : 'Save Team Members'}
                  </Button>

                  {isFormValid() && (
                    <Button
                      variant="outlined"
                      onClick={() => window.location.href = '/user-dashboard/mandal/ganesh-details'}
                      size="large"
                    >
                      Next: Ganesh Details
                    </Button>
                  )}
                </Box>
              </Grid>
            </Grid>
          </form>
        </CardContent>
      </Card>
    </Box>
  );
};

export default MandalTeamMembers;
