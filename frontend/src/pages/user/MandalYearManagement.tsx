import React, { useState } from 'react';
import {
  Typo<PERSON>,
  <PERSON>,
  Card,
  CardContent,
  Button,
  Grid,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import {
  CalendarToday as YearIcon,
  Add as AddIcon,
  ContentCopy as CopyIcon,
  Edit as EditIcon,
} from '@mui/icons-material';

const MandalYearManagement: React.FC = () => {
  const [years, setYears] = useState(['2024', '2025']);
  const [selectedYear, setSelectedYear] = useState('2025');
  const [openDialog, setOpenDialog] = useState(false);
  const [newYear, setNewYear] = useState('');

  const handleDuplicateYear = (fromYear: string) => {
    const currentYear = new Date().getFullYear().toString();
    if (!years.includes(currentYear)) {
      setYears([...years, currentYear]);
      setSelectedYear(currentYear);
    }
  };

  const handleAddYear = () => {
    if (newYear && !years.includes(newYear)) {
      setYears([...years, newYear]);
      setSelectedYear(newYear);
      setNewYear('');
      setOpenDialog(false);
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
        <YearIcon color="primary" />
        <Typography variant="h5" color="primary">
          Year Management
        </Typography>
      </Box>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Manage your mandal details for different years. You can duplicate previous year data and edit as needed.
      </Typography>

      <Grid container spacing={3}>
        {/* Year Selection */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Available Years
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                {years.map((year) => (
                  <Chip
                    key={year}
                    label={year}
                    color={year === selectedYear ? 'primary' : 'default'}
                    variant={year === selectedYear ? 'filled' : 'outlined'}
                    onClick={() => setSelectedYear(year)}
                    clickable
                  />
                ))}
              </Box>
              <Button
                startIcon={<AddIcon />}
                variant="outlined"
                onClick={() => setOpenDialog(true)}
              >
                Add New Year
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Current Year Details */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {selectedYear} Details
              </Typography>
              
              <Alert severity="info" sx={{ mb: 2 }}>
                Year-wise management functionality coming soon. You'll be able to:
                <ul>
                  <li>Duplicate previous year data</li>
                  <li>Edit specific fields for each year</li>
                  <li>Manage different Ganesha details per year</li>
                  <li>Track year-wise photos and events</li>
                </ul>
              </Alert>

              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  startIcon={<CopyIcon />}
                  variant="outlined"
                  onClick={() => handleDuplicateYear(selectedYear)}
                >
                  Duplicate from Previous Year
                </Button>
                <Button
                  startIcon={<EditIcon />}
                  variant="contained"
                >
                  Edit {selectedYear} Details
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Add Year Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <DialogTitle>Add New Year</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Year"
            type="number"
            fullWidth
            variant="outlined"
            value={newYear}
            onChange={(e) => setNewYear(e.target.value)}
            placeholder="e.g., 2026"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button onClick={handleAddYear} variant="contained">Add Year</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MandalYearManagement;
