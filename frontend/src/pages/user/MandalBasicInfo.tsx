import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Save as SaveIcon,
  CheckCircle as CompleteIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { mandalAPI } from '../../utils/api';
import toast from 'react-hot-toast';

interface BasicInfoFormData {
  name: string;
  registrationNumber: string;
  establishmentYear: number;
  category: 'mandal' | 'home' | 'celebrity';
  description: string;
  location: {
    address: string;
    city: string;
    state: string;
    pincode: string;
  };
}

const MandalBasicInfo: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [saved, setSaved] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
    watch,
    reset,
  } = useForm<BasicInfoFormData>({
    defaultValues: {
      name: '',
      registrationNumber: '',
      establishmentYear: new Date().getFullYear(),
      category: 'mandal',
      description: '',
      location: {
        address: '',
        city: '',
        state: '',
        pincode: '',
      },
    },
  });

  useEffect(() => {
    // Load existing data if available
    loadExistingData();
  }, []);

  const loadExistingData = async () => {
    try {
      // This would load existing mandal data
      // const response = await mandalAPI.getMyMandals();
      // if (response.data.mandals.length > 0) {
      //   reset(response.data.mandals[0]);
      //   setSaved(true);
      // }
    } catch (error) {
      console.error('Failed to load existing data');
    }
  };

  const onSubmit = async (data: BasicInfoFormData) => {
    try {
      setLoading(true);
      await mandalAPI.saveDraft(data);
      toast.success('Basic information saved successfully!');
      setSaved(true);
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to save basic information');
    } finally {
      setLoading(false);
    }
  };

  const isFormValid = () => {
    const formData = watch();
    return (
      formData.name &&
      formData.registrationNumber &&
      formData.establishmentYear &&
      formData.category &&
      formData.location.address &&
      formData.location.city &&
      formData.location.state &&
      formData.location.pincode
    );
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
        <InfoIcon color="primary" />
        <Typography variant="h5" color="primary">
          Basic Information
        </Typography>
        {saved && <CompleteIcon color="success" />}
      </Box>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Provide basic details about your mandal. This information will be displayed to visitors.
      </Typography>

      {saved && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Basic information has been saved. You can proceed to the next section.
        </Alert>
      )}

      <Card>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Grid container spacing={3}>
              {/* Mandal Details */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Mandal Details
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="name"
                  control={control}
                  rules={{ required: 'Mandal name is required' }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Mandal Name"
                      error={!!errors.name}
                      helperText={errors.name?.message}
                      placeholder="Enter your mandal name"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="registrationNumber"
                  control={control}
                  rules={{ required: 'Registration number is required' }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Registration Number"
                      error={!!errors.registrationNumber}
                      helperText={errors.registrationNumber?.message}
                      placeholder="Enter registration number"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="establishmentYear"
                  control={control}
                  rules={{ 
                    required: 'Establishment year is required',
                    min: { value: 1800, message: 'Invalid year' },
                    max: { value: new Date().getFullYear(), message: 'Year cannot be in future' }
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      type="number"
                      label="Establishment Year"
                      error={!!errors.establishmentYear}
                      helperText={errors.establishmentYear?.message}
                      placeholder="e.g., 1995"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="category"
                  control={control}
                  rules={{ required: 'Category is required' }}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!errors.category}>
                      <InputLabel>Category</InputLabel>
                      <Select {...field} label="Category">
                        <MenuItem value="mandal">Mandal</MenuItem>
                        <MenuItem value="home">Home</MenuItem>
                        <MenuItem value="celebrity">Celebrity</MenuItem>
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      multiline
                      rows={3}
                      label="Description (Optional)"
                      placeholder="Brief description about your mandal..."
                    />
                  )}
                />
              </Grid>

              {/* Location Details */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                  Location Details
                </Typography>
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="location.address"
                  control={control}
                  rules={{ required: 'Address is required' }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      multiline
                      rows={2}
                      label="Complete Address"
                      error={!!errors.location?.address}
                      helperText={errors.location?.address?.message}
                      placeholder="Enter complete address with landmarks"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <Controller
                  name="location.city"
                  control={control}
                  rules={{ required: 'City is required' }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="City"
                      error={!!errors.location?.city}
                      helperText={errors.location?.city?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <Controller
                  name="location.state"
                  control={control}
                  rules={{ required: 'State is required' }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="State"
                      error={!!errors.location?.state}
                      helperText={errors.location?.state?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <Controller
                  name="location.pincode"
                  control={control}
                  rules={{ 
                    required: 'Pincode is required',
                    pattern: { value: /^[1-9][0-9]{5}$/, message: 'Invalid pincode' }
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Pincode"
                      error={!!errors.location?.pincode}
                      helperText={errors.location?.pincode?.message}
                    />
                  )}
                />
              </Grid>

              {/* Action Buttons */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                  <Button
                    type="submit"
                    variant="contained"
                    startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                    disabled={loading}
                    size="large"
                  >
                    {loading ? 'Saving...' : 'Save Basic Information'}
                  </Button>

                  {isFormValid() && (
                    <Button
                      variant="outlined"
                      onClick={() => window.location.href = '/user-dashboard/mandal/contact-info'}
                      size="large"
                    >
                      Next: Contact Details
                    </Button>
                  )}
                </Box>
              </Grid>
            </Grid>
          </form>
        </CardContent>
      </Card>
    </Box>
  );
};

export default MandalBasicInfo;
