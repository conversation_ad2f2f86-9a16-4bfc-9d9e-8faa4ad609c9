import React, { useState, useEffect } from 'react';
import {
  Con<PERSON>er,
  Paper,
  Typography,
  Button,
  Box,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Divider,
} from '@mui/material';
import {
  AdminPanelSettings,
  CheckCircle,
  Warning,
} from '@mui/icons-material';
import axios from 'axios';

const Setup: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [setupStatus, setSetupStatus] = useState<{
    adminExists: boolean;
    setupRequired: boolean;
    emailExists: boolean;
    needsReset: boolean;
  } | null>(null);
  const [adminCredentials, setAdminCredentials] = useState<{
    email: string;
    password: string;
  } | null>(null);
  const [error, setError] = useState('');

  useEffect(() => {
    checkSetupStatus();
  }, []);

  const checkSetupStatus = async () => {
    try {
      const response = await axios.get('http://localhost:5001/api/setup/status');
      setSetupStatus(response.data.data);
    } catch (err) {
      setError('Failed to check setup status');
    }
  };

  const initializeAdmin = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await axios.post('http://localhost:5001/api/setup/init-admin');

      setAdminCredentials(response.data.data.credentials);
      await checkSetupStatus(); // Refresh status

    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to initialize admin');
    } finally {
      setLoading(false);
    }
  };

  const resetAdmin = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await axios.post('http://localhost:5001/api/setup/reset-admin');

      setAdminCredentials(response.data.data.credentials);
      await checkSetupStatus(); // Refresh status

    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to reset admin');
    } finally {
      setLoading(false);
    }
  };

  if (!setupStatus) {
    return (
      <Container maxWidth="sm" sx={{ py: 8 }}>
        <Box display="flex" justifyContent="center">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ py: 8 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        <Box textAlign="center" mb={4}>
          <AdminPanelSettings sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
          <Typography variant="h4" gutterBottom color="primary">
            🕉️ Ganesh Darshan Setup
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Initialize your super admin account to manage the platform
          </Typography>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {setupStatus.adminExists ? (
          <Card sx={{ bgcolor: 'success.light', color: 'success.contrastText' }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <CheckCircle />
                <Box>
                  <Typography variant="h6">
                    Setup Complete!
                  </Typography>
                  <Typography variant="body2">
                    Super admin account is already configured.
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        ) : setupStatus.needsReset ? (
          <Card sx={{ bgcolor: 'warning.light', color: 'warning.contrastText', mb: 3 }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <Warning />
                <Box>
                  <Typography variant="h6">
                    Admin Reset Required
                  </Typography>
                  <Typography variant="body2">
                    Admin email exists but needs super admin privileges. Click below to reset.
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        ) : (
          <Card sx={{ bgcolor: 'warning.light', color: 'warning.contrastText', mb: 3 }}>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <Warning />
                <Box>
                  <Typography variant="h6">
                    Setup Required
                  </Typography>
                  <Typography variant="body2">
                    No super admin account found. Click below to initialize.
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        )}

        {adminCredentials && (
          <Alert severity="success" sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Super Admin Created Successfully!
            </Typography>
            <Typography variant="body2" gutterBottom>
              <strong>Email:</strong> {adminCredentials.email}
            </Typography>
            <Typography variant="body2" gutterBottom>
              <strong>Password:</strong> {adminCredentials.password}
            </Typography>
            <Typography variant="caption" color="error">
              ⚠️ Please save these credentials and change the password after first login!
            </Typography>
          </Alert>
        )}

        <Box textAlign="center">
          {setupStatus.setupRequired && (
            <Button
              variant="contained"
              size="large"
              onClick={initializeAdmin}
              disabled={loading}
              sx={{ minWidth: 200 }}
            >
              {loading ? (
                <>
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                  Initializing...
                </>
              ) : (
                'Initialize Super Admin'
              )}
            </Button>
          )}

          {setupStatus.needsReset && (
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                size="large"
                onClick={resetAdmin}
                disabled={loading}
                color="warning"
                sx={{ minWidth: 200 }}
              >
                {loading ? (
                  <>
                    <CircularProgress size={20} sx={{ mr: 1 }} />
                    Resetting...
                  </>
                ) : (
                  'Reset Admin Credentials'
                )}
              </Button>
              <Button
                variant="outlined"
                size="large"
                onClick={initializeAdmin}
                disabled={loading}
                sx={{ minWidth: 200 }}
              >
                {loading ? (
                  <>
                    <CircularProgress size={20} sx={{ mr: 1 }} />
                    Initializing...
                  </>
                ) : (
                  'Force Initialize'
                )}
              </Button>
            </Box>
          )}

          {setupStatus.adminExists && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Ready to Login
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  size="large"
                  href="/login"
                  sx={{ minWidth: 200 }}
                >
                  Go to Login
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  onClick={resetAdmin}
                  disabled={loading}
                  color="warning"
                >
                  Reset Password
                </Button>
              </Box>
            </Box>
          )}
        </Box>

        <Divider sx={{ my: 4 }} />

        <Box>
          <Typography variant="h6" gutterBottom>
            Default Super Admin Credentials:
          </Typography>
          <Typography variant="body2" color="text.secondary">
            <strong>Email:</strong> <EMAIL><br />
            <strong>Password:</strong> admin123
          </Typography>
          <Typography variant="caption" color="error" sx={{ mt: 1, display: 'block' }}>
            ⚠️ Change these credentials immediately after first login for security!
          </Typography>
        </Box>
      </Paper>
    </Container>
  );
};

export default Setup;
