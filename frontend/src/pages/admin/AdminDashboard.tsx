import React, { useState } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  Too<PERSON>bar,
  List,
  Typography,
  Divider,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  useTheme,
  useMediaQuery,
  IconButton,
} from '@mui/material';
import {
  Dashboard,
  People,
  AccountBalance as Temple,
  Menu as MenuIcon,
  Assessment,
  Settings,
} from '@mui/icons-material';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import AdminStats from './AdminStats';
import UserManagement from './UserManagement';
import MandalManagement from './MandalManagement';
import SuperAdminDashboard from './SuperAdminDashboard';

const drawerWidth = 240;

const AdminDashboard: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const menuItems = [
    {
      text: 'Dashboard',
      icon: <Dashboard />,
      path: '/admin',
    },
    {
      text: 'Super Admin',
      icon: <Settings />,
      path: '/admin/super',
    },
    {
      text: 'User Management',
      icon: <People />,
      path: '/admin/users',
    },
    {
      text: 'Mandal Management',
      icon: <Temple />,
      path: '/admin/mandals',
    },
    {
      text: 'Reports',
      icon: <Assessment />,
      path: '/admin/reports',
    },
  ];

  const drawer = (
    <div>
      <Toolbar>
        <Typography variant="h6" noWrap component="div" color="primary">
          Admin Panel
        </Typography>
      </Toolbar>
      <Divider />
      <List>
        {menuItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              selected={location.pathname === item.path}
              onClick={() => {
                navigate(item.path);
                if (isMobile) {
                  setMobileOpen(false);
                }
              }}
            >
              <ListItemIcon>{item.icon}</ListItemIcon>
              <ListItemText primary={item.text} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </div>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      {/* Mobile App Bar */}
      {isMobile && (
        <AppBar
          position="fixed"
          sx={{
            width: '100%',
            ml: 0,
            top: 64, // Below main navbar
          }}
        >
          <Toolbar>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
            <Typography variant="h6" noWrap component="div">
              Admin Dashboard
            </Typography>
          </Toolbar>
        </AppBar>
      )}

      {/* Sidebar */}
      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        <Drawer
          variant={isMobile ? 'temporary' : 'permanent'}
          open={isMobile ? mobileOpen : true}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile.
          }}
          sx={{
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              top: isMobile ? 128 : 64, // Below navbar(s)
              height: isMobile ? 'calc(100% - 128px)' : 'calc(100% - 64px)',
            },
          }}
        >
          {drawer}
        </Drawer>
      </Box>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mt: isMobile ? 8 : 0, // Account for mobile app bar
        }}
      >
        <Routes>
          <Route path="/" element={<AdminStats />} />
          <Route path="/super" element={<SuperAdminDashboard />} />
          <Route path="/users" element={<UserManagement />} />
          <Route path="/mandals" element={<MandalManagement />} />
          <Route path="/reports" element={
            <Box>
              <Typography variant="h4" gutterBottom>
                Reports
              </Typography>
              <Typography variant="body1">
                Reports functionality coming soon...
              </Typography>
            </Box>
          } />
        </Routes>
      </Box>
    </Box>
  );
};

export default AdminDashboard;
