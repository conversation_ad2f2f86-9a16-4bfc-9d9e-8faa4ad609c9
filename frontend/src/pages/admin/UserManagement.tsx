import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Chip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TablePagination,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  CheckCircle,
  Cancel,
  Search,
} from '@mui/icons-material';
import { adminAPI } from '../../utils/api';
import { User, ApiResponse } from '../../types';
import toast from 'react-hot-toast';

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    user: User | null;
    action: 'approve' | 'reject';
  }>({
    open: false,
    user: null,
    action: 'approve',
  });

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const params: any = {
        page: page + 1,
        limit: rowsPerPage,
      };

      if (statusFilter !== 'all') {
        params.isApproved = statusFilter === 'approved';
      }

      const response = await adminAPI.getAllUsers(params);
      setUsers(response.data.users);
      setTotal(response.data.pagination.total);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [page, rowsPerPage, statusFilter]);

  const handleApprove = async (user: User) => {
    try {
      await adminAPI.approveUser(user._id);
      toast.success(`User ${user.firstName} ${user.lastName} approved successfully`);
      fetchUsers();
    } catch (err: any) {
      toast.error(err.response?.data?.message || 'Failed to approve user');
    }
    setConfirmDialog({ open: false, user: null, action: 'approve' });
  };

  const handleReject = async (user: User) => {
    try {
      await adminAPI.rejectUser(user._id);
      toast.success(`User ${user.firstName} ${user.lastName} rejected`);
      fetchUsers();
    } catch (err: any) {
      toast.error(err.response?.data?.message || 'Failed to reject user');
    }
    setConfirmDialog({ open: false, user: null, action: 'reject' });
  };

  const getStatusChip = (user: User) => {
    if (user.isApproved && user.isActive) {
      return <Chip label="Approved" color="success" size="small" />;
    } else if (!user.isApproved) {
      return <Chip label="Pending" color="warning" size="small" />;
    } else {
      return <Chip label="Rejected" color="error" size="small" />;
    }
  };

  const filteredUsers = users.filter(user =>
    search === '' ||
    user.firstName.toLowerCase().includes(search.toLowerCase()) ||
    user.lastName.toLowerCase().includes(search.toLowerCase()) ||
    user.email.toLowerCase().includes(search.toLowerCase())
  );

  if (loading && users.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        User Management
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Approve or reject user registrations
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
        <TextField
          placeholder="Search users..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
          sx={{ minWidth: 250 }}
        />
        <FormControl sx={{ minWidth: 150 }}>
          <InputLabel>Status</InputLabel>
          <Select
            value={statusFilter}
            label="Status"
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <MenuItem value="all">All</MenuItem>
            <MenuItem value="approved">Approved</MenuItem>
            <MenuItem value="pending">Pending</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Users Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Phone</TableCell>
              <TableCell>Location</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Registered</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredUsers.map((user) => (
              <TableRow key={user._id}>
                <TableCell>
                  {user.firstName} {user.lastName}
                </TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>{user.phone}</TableCell>
                <TableCell>
                  {user.address.city}, {user.address.state}
                </TableCell>
                <TableCell>{getStatusChip(user)}</TableCell>
                <TableCell>
                  {new Date(user.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    {!user.isApproved && (
                      <>
                        <Button
                          size="small"
                          variant="contained"
                          color="success"
                          startIcon={<CheckCircle />}
                          onClick={() =>
                            setConfirmDialog({
                              open: true,
                              user,
                              action: 'approve',
                            })
                          }
                        >
                          Approve
                        </Button>
                        <Button
                          size="small"
                          variant="outlined"
                          color="error"
                          startIcon={<Cancel />}
                          onClick={() =>
                            setConfirmDialog({
                              open: true,
                              user,
                              action: 'reject',
                            })
                          }
                        >
                          Reject
                        </Button>
                      </>
                    )}
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component="div"
        count={total}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={(_, newPage) => setPage(newPage)}
        onRowsPerPageChange={(event) => {
          setRowsPerPage(parseInt(event.target.value, 10));
          setPage(0);
        }}
      />

      {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialog.open}
        onClose={() => setConfirmDialog({ open: false, user: null, action: 'approve' })}
      >
        <DialogTitle>
          {confirmDialog.action === 'approve' ? 'Approve User' : 'Reject User'}
        </DialogTitle>
        <DialogContent>
          Are you sure you want to {confirmDialog.action} user{' '}
          <strong>
            {confirmDialog.user?.firstName} {confirmDialog.user?.lastName}
          </strong>
          ?
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setConfirmDialog({ open: false, user: null, action: 'approve' })}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              if (confirmDialog.user) {
                if (confirmDialog.action === 'approve') {
                  handleApprove(confirmDialog.user);
                } else {
                  handleReject(confirmDialog.user);
                }
              }
            }}
            color={confirmDialog.action === 'approve' ? 'success' : 'error'}
            variant="contained"
          >
            {confirmDialog.action === 'approve' ? 'Approve' : 'Reject'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserManagement;
