import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
  Box,
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  CircularProgress,
  Divider,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
} from '@mui/material';
import {
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { mandalAPI } from '../../utils/api';
import { Mandal } from '../../types';
import toast from 'react-hot-toast';

const MandalDetails: React.FC = () => {
  const [mandals, setMandals] = useState<Mandal[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editData, setEditData] = useState<Partial<Mandal>>({});

  useEffect(() => {
    fetchMandals();
  }, []);

  const fetchMandals = async () => {
    try {
      setLoading(true);
      const response = await mandalAPI.getAll({ status: 'approved', limit: 100 });
      setMandals(response.data.mandals);
    } catch (error: any) {
      toast.error('Failed to fetch mandals');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (mandal: Mandal) => {
    setEditingId(mandal._id);
    setEditData({
      ganeshDetails: {
        ...mandal.ganeshDetails,
        murtikarName: mandal.ganeshDetails?.murtikarName || '',
        festivalDuration: mandal.ganeshDetails?.festivalDuration || '1.5',
      },
      timings: mandal.timings,
      facilities: mandal.facilities,
    });
  };

  const handleSave = async (mandalId: string) => {
    try {
      await mandalAPI.update(mandalId, editData);
      toast.success('Mandal details updated successfully');
      setEditingId(null);
      fetchMandals();
    } catch (error: any) {
      toast.error('Failed to update mandal details');
    }
  };

  const handleCancel = () => {
    setEditingId(null);
    setEditData({});
  };

  const updateEditData = (path: string, value: any) => {
    const keys = path.split('.');
    const newData = { ...editData };
    let current = newData;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    setEditData(newData);
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
          <CircularProgress size={40} />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom color="primary">
        🕉️ Manage Mandal Details
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Update additional details for approved mandals
      </Typography>

      <Grid container spacing={3}>
        {mandals.map((mandal) => (
          <Grid item xs={12} key={mandal._id}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6">{mandal.name}</Typography>
                  <Box>
                    {editingId === mandal._id ? (
                      <>
                        <IconButton onClick={() => handleSave(mandal._id)} color="primary">
                          <SaveIcon />
                        </IconButton>
                        <IconButton onClick={handleCancel} color="secondary">
                          <CancelIcon />
                        </IconButton>
                      </>
                    ) : (
                      <IconButton onClick={() => handleEdit(mandal)} color="primary">
                        <EditIcon />
                      </IconButton>
                    )}
                  </Box>
                </Box>

                <Grid container spacing={3}>
                  {/* Basic Info */}
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" color="text.secondary">Location</Typography>
                    <Typography variant="body2">
                      {mandal.location.city}, {mandal.location.state}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" color="text.secondary">Category</Typography>
                    <Chip label={mandal.category} size="small" />
                  </Grid>

                  {/* Ganesh Details */}
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="h6" gutterBottom>Ganesh Details</Typography>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    {editingId === mandal._id ? (
                      <TextField
                        fullWidth
                        label="Murtikar Name"
                        value={editData.ganeshDetails?.murtikarName || ''}
                        onChange={(e) => updateEditData('ganeshDetails.murtikarName', e.target.value)}
                        size="small"
                      />
                    ) : (
                      <>
                        <Typography variant="subtitle2" color="text.secondary">Murtikar Name</Typography>
                        <Typography variant="body2">
                          {mandal.ganeshDetails?.murtikarName || 'Not specified'}
                        </Typography>
                      </>
                    )}
                  </Grid>

                  <Grid item xs={12} md={4}>
                    {editingId === mandal._id ? (
                      <FormControl fullWidth size="small">
                        <InputLabel>Festival Duration</InputLabel>
                        <Select
                          value={editData.ganeshDetails?.festivalDuration || '1.5'}
                          onChange={(e) => updateEditData('ganeshDetails.festivalDuration', e.target.value)}
                          label="Festival Duration"
                        >
                          <MenuItem value="1.5">1½ Days</MenuItem>
                          <MenuItem value="3">3 Days</MenuItem>
                          <MenuItem value="5">5 Days</MenuItem>
                          <MenuItem value="7">7 Days</MenuItem>
                          <MenuItem value="10">10 Days</MenuItem>
                        </Select>
                      </FormControl>
                    ) : (
                      <>
                        <Typography variant="subtitle2" color="text.secondary">Festival Duration</Typography>
                        <Typography variant="body2">
                          {mandal.ganeshDetails?.festivalDuration ? 
                            `${mandal.ganeshDetails.festivalDuration} Days` : 
                            'Not specified'
                          }
                        </Typography>
                      </>
                    )}
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Typography variant="subtitle2" color="text.secondary">Murti Details</Typography>
                    <Typography variant="body2">
                      {mandal.ganeshDetails?.height} • {mandal.ganeshDetails?.weight}
                    </Typography>
                  </Grid>

                  {/* Timings */}
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="h6" gutterBottom>Darshan Timings</Typography>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    {editingId === mandal._id ? (
                      <TextField
                        fullWidth
                        type="time"
                        label="Opening Time"
                        value={editData.timings?.openTime || '06:00'}
                        onChange={(e) => updateEditData('timings.openTime', e.target.value)}
                        size="small"
                        InputLabelProps={{ shrink: true }}
                      />
                    ) : (
                      <>
                        <Typography variant="subtitle2" color="text.secondary">Opening Time</Typography>
                        <Typography variant="body2">
                          {mandal.timings?.openTime || '06:00'}
                        </Typography>
                      </>
                    )}
                  </Grid>

                  <Grid item xs={12} md={6}>
                    {editingId === mandal._id ? (
                      <TextField
                        fullWidth
                        type="time"
                        label="Closing Time"
                        value={editData.timings?.closeTime || '22:00'}
                        onChange={(e) => updateEditData('timings.closeTime', e.target.value)}
                        size="small"
                        InputLabelProps={{ shrink: true }}
                      />
                    ) : (
                      <>
                        <Typography variant="subtitle2" color="text.secondary">Closing Time</Typography>
                        <Typography variant="body2">
                          {mandal.timings?.closeTime || '22:00'}
                        </Typography>
                      </>
                    )}
                  </Grid>

                  {/* Facilities */}
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="h6" gutterBottom>Facilities</Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      {mandal.facilities?.parking && <Chip label="Parking" size="small" />}
                      {mandal.facilities?.wheelchairAccess && <Chip label="Wheelchair Access" size="small" />}
                      {mandal.facilities?.restrooms && <Chip label="Restrooms" size="small" />}
                      {mandal.facilities?.foodStalls && <Chip label="Food Stalls" size="small" />}
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {mandals.length === 0 && (
        <Alert severity="info">
          No approved mandals found.
        </Alert>
      )}
    </Container>
  );
};

export default MandalDetails;
