import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  <PERSON>,
  Card,
  CardContent,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Button,
  Chip,
  Alert,
  CircularProgress,
  Divider,
  Avatar,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Visibility,
  CheckCircle,
  Cancel,
  Person,
  AccountBalance as Temple,
  PendingActions,
} from '@mui/icons-material';
import { adminAPI } from '../../utils/api';
import { User, Mandal } from '../../types';
import toast from 'react-hot-toast';

const SuperAdminDashboard: React.FC = () => {
  const [pendingUsers, setPendingUsers] = useState<User[]>([]);
  const [pendingMandals, setPendingMandals] = useState<Mandal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedItem, setSelectedItem] = useState<{
    type: 'user' | 'mandal';
    data: User | Mandal;
  } | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  useEffect(() => {
    fetchPendingItems();
  }, []);

  const fetchPendingItems = async () => {
    try {
      setLoading(true);
      const [usersResponse, mandalsResponse] = await Promise.all([
        adminAPI.getAllUsers({ isApproved: false, limit: 50 }),
        adminAPI.getAllMandals({ status: 'pending', limit: 50 })
      ]);

      setPendingUsers(usersResponse.data.users);
      setPendingMandals(mandalsResponse.data.mandals);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch pending items');
    } finally {
      setLoading(false);
    }
  };

  const handleApproveUser = async (userId: string) => {
    try {
      setActionLoading(`approve-user-${userId}`);
      await adminAPI.approveUser(userId);
      toast.success('User approved successfully');
      fetchPendingItems();
    } catch (err: any) {
      toast.error(err.response?.data?.message || 'Failed to approve user');
    } finally {
      setActionLoading(null);
    }
  };

  const handleRejectUser = async (userId: string) => {
    try {
      setActionLoading(`reject-user-${userId}`);
      await adminAPI.rejectUser(userId);
      toast.success('User rejected');
      fetchPendingItems();
    } catch (err: any) {
      toast.error(err.response?.data?.message || 'Failed to reject user');
    } finally {
      setActionLoading(null);
    }
  };

  const handleApproveMandal = async (mandalId: string) => {
    try {
      setActionLoading(`approve-mandal-${mandalId}`);
      await adminAPI.approveMandal(mandalId);
      toast.success('Mandal approved successfully');
      fetchPendingItems();
    } catch (err: any) {
      toast.error(err.response?.data?.message || 'Failed to approve mandal');
    } finally {
      setActionLoading(null);
    }
  };

  const handleRejectMandal = async (mandalId: string) => {
    try {
      setActionLoading(`reject-mandal-${mandalId}`);
      await adminAPI.rejectMandal(mandalId, 'Rejected by admin');
      toast.success('Mandal rejected');
      fetchPendingItems();
    } catch (err: any) {
      toast.error(err.response?.data?.message || 'Failed to reject mandal');
    } finally {
      setActionLoading(null);
    }
  };

  const handleViewDetails = (type: 'user' | 'mandal', data: User | Mandal) => {
    setSelectedItem({ type, data });
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
          <CircularProgress size={40} />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom color="primary">
        🕉️ Super Admin Dashboard
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Manage pending user registrations and mandal submissions
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={4}>
        {/* Pending Users */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2} mb={2}>
                <Person color="primary" />
                <Typography variant="h6">
                  Pending User Approvals
                </Typography>
                <Chip 
                  label={pendingUsers.length} 
                  color="warning" 
                  size="small" 
                />
              </Box>
              
              {pendingUsers.length === 0 ? (
                <Alert severity="success">
                  No pending user approvals! 🎉
                </Alert>
              ) : (
                <List>
                  {pendingUsers.map((user, index) => (
                    <React.Fragment key={user._id}>
                      <ListItem>
                        <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                          {user.firstName[0]}
                        </Avatar>
                        <ListItemText
                          primary={`${user.firstName} ${user.lastName}`}
                          secondary={
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                {user.email}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                {user.phone} • {user.address.city}, {user.address.state}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                Registered: {new Date(user.createdAt).toLocaleDateString()}
                              </Typography>
                            </Box>
                          }
                        />
                        <ListItemSecondaryAction>
                          <Box display="flex" gap={1}>
                            <IconButton
                              size="small"
                              onClick={() => handleViewDetails('user', user)}
                              color="info"
                            >
                              <Visibility />
                            </IconButton>
                            <Button
                              size="small"
                              variant="contained"
                              color="success"
                              onClick={() => handleApproveUser(user._id)}
                              disabled={actionLoading === `approve-user-${user._id}`}
                              startIcon={actionLoading === `approve-user-${user._id}` ? 
                                <CircularProgress size={16} /> : <CheckCircle />}
                            >
                              Approve
                            </Button>
                            <Button
                              size="small"
                              variant="outlined"
                              color="error"
                              onClick={() => handleRejectUser(user._id)}
                              disabled={actionLoading === `reject-user-${user._id}`}
                              startIcon={actionLoading === `reject-user-${user._id}` ? 
                                <CircularProgress size={16} /> : <Cancel />}
                            >
                              Reject
                            </Button>
                          </Box>
                        </ListItemSecondaryAction>
                      </ListItem>
                      {index < pendingUsers.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Pending Mandals */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2} mb={2}>
                <Temple color="primary" />
                <Typography variant="h6">
                  Pending Mandal Approvals
                </Typography>
                <Chip 
                  label={pendingMandals.length} 
                  color="warning" 
                  size="small" 
                />
              </Box>
              
              {pendingMandals.length === 0 ? (
                <Alert severity="success">
                  No pending mandal approvals! 🎉
                </Alert>
              ) : (
                <List>
                  {pendingMandals.map((mandal, index) => (
                    <React.Fragment key={mandal._id}>
                      <ListItem>
                        <Avatar sx={{ mr: 2, bgcolor: 'secondary.main' }}>
                          🕉️
                        </Avatar>
                        <ListItemText
                          primary={mandal.name}
                          secondary={
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                {mandal.location.city}, {mandal.location.state}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Est. {mandal.establishmentYear} • {mandal.category}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                Submitted: {new Date(mandal.createdAt).toLocaleDateString()}
                              </Typography>
                            </Box>
                          }
                        />
                        <ListItemSecondaryAction>
                          <Box display="flex" gap={1}>
                            <IconButton
                              size="small"
                              onClick={() => handleViewDetails('mandal', mandal)}
                              color="info"
                            >
                              <Visibility />
                            </IconButton>
                            <Button
                              size="small"
                              variant="contained"
                              color="success"
                              onClick={() => handleApproveMandal(mandal._id)}
                              disabled={actionLoading === `approve-mandal-${mandal._id}`}
                              startIcon={actionLoading === `approve-mandal-${mandal._id}` ? 
                                <CircularProgress size={16} /> : <CheckCircle />}
                            >
                              Approve
                            </Button>
                            <Button
                              size="small"
                              variant="outlined"
                              color="error"
                              onClick={() => handleRejectMandal(mandal._id)}
                              disabled={actionLoading === `reject-mandal-${mandal._id}`}
                              startIcon={actionLoading === `reject-mandal-${mandal._id}` ? 
                                <CircularProgress size={16} /> : <Cancel />}
                            >
                              Reject
                            </Button>
                          </Box>
                        </ListItemSecondaryAction>
                      </ListItem>
                      {index < pendingMandals.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* View Details Modal */}
      <Dialog
        open={!!selectedItem}
        onClose={() => setSelectedItem(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedItem?.type === 'user' ? 'User Details' : 'Mandal Details'}
        </DialogTitle>
        <DialogContent>
          {selectedItem?.type === 'user' ? (
            <UserDetailsView user={selectedItem.data as User} />
          ) : (
            <MandalDetailsView mandal={selectedItem?.data as Mandal} />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSelectedItem(null)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

// Detail View Components
const UserDetailsView: React.FC<{ user: User }> = ({ user }) => (
  <Box>
    <Grid container spacing={2}>
      <Grid item xs={12} md={6}>
        <Typography variant="subtitle2" color="text.secondary">Full Name</Typography>
        <Typography variant="body1">{user.firstName} {user.lastName}</Typography>
      </Grid>
      <Grid item xs={12} md={6}>
        <Typography variant="subtitle2" color="text.secondary">Email</Typography>
        <Typography variant="body1">{user.email}</Typography>
      </Grid>
      <Grid item xs={12} md={6}>
        <Typography variant="subtitle2" color="text.secondary">Phone</Typography>
        <Typography variant="body1">{user.phone}</Typography>
      </Grid>
      <Grid item xs={12} md={6}>
        <Typography variant="subtitle2" color="text.secondary">Role</Typography>
        <Chip label={user.role} size="small" />
      </Grid>
      <Grid item xs={12}>
        <Typography variant="subtitle2" color="text.secondary">Address</Typography>
        <Typography variant="body1">
          {user.address.city}, {user.address.state}
        </Typography>
      </Grid>
      <Grid item xs={12} md={6}>
        <Typography variant="subtitle2" color="text.secondary">Registration Date</Typography>
        <Typography variant="body1">{new Date(user.createdAt).toLocaleDateString()}</Typography>
      </Grid>
      <Grid item xs={12} md={6}>
        <Typography variant="subtitle2" color="text.secondary">Status</Typography>
        <Chip
          label={user.isApproved ? 'Approved' : 'Pending'}
          color={user.isApproved ? 'success' : 'warning'}
          size="small"
        />
      </Grid>
    </Grid>
  </Box>
);

const MandalDetailsView: React.FC<{ mandal: Mandal }> = ({ mandal }) => (
  <Box>
    <Grid container spacing={2}>
      <Grid item xs={12} md={6}>
        <Typography variant="subtitle2" color="text.secondary">Mandal Name</Typography>
        <Typography variant="body1">{mandal.name}</Typography>
      </Grid>
      <Grid item xs={12} md={6}>
        <Typography variant="subtitle2" color="text.secondary">Registration Number</Typography>
        <Typography variant="body1">{mandal.registrationNumber}</Typography>
      </Grid>
      <Grid item xs={12} md={6}>
        <Typography variant="subtitle2" color="text.secondary">Establishment Year</Typography>
        <Typography variant="body1">{mandal.establishmentYear}</Typography>
      </Grid>
      <Grid item xs={12} md={6}>
        <Typography variant="subtitle2" color="text.secondary">Category</Typography>
        <Chip label={mandal.category} size="small" />
      </Grid>
      <Grid item xs={12}>
        <Typography variant="subtitle2" color="text.secondary">Location</Typography>
        <Typography variant="body1">
          {mandal.location.address}, {mandal.location.city}, {mandal.location.state} - {mandal.location.pincode}
        </Typography>
      </Grid>
      <Grid item xs={12}>
        <Typography variant="subtitle2" color="text.secondary">Contact Information</Typography>
        <Typography variant="body1">
          Email: {mandal.contactInfo?.email}<br />
          Phone: {mandal.contactInfo?.phone}
        </Typography>
      </Grid>
      <Grid item xs={12}>
        <Typography variant="subtitle2" color="text.secondary">Ganesh Details</Typography>
        <Typography variant="body1">
          Height: {mandal.ganeshDetails?.height}<br />
          Theme: {mandal.ganeshDetails?.theme}
        </Typography>
      </Grid>
      <Grid item xs={12}>
        <Typography variant="subtitle2" color="text.secondary">Description</Typography>
        <Typography variant="body1">{mandal.description}</Typography>
      </Grid>
      <Grid item xs={12} md={6}>
        <Typography variant="subtitle2" color="text.secondary">Submission Date</Typography>
        <Typography variant="body1">{new Date(mandal.createdAt).toLocaleDateString()}</Typography>
      </Grid>
      <Grid item xs={12} md={6}>
        <Typography variant="subtitle2" color="text.secondary">Status</Typography>
        <Chip
          label={mandal.status}
          color={mandal.status === 'approved' ? 'success' : 'warning'}
          size="small"
        />
      </Grid>
    </Grid>
  </Box>
);

export default SuperAdminDashboard;
