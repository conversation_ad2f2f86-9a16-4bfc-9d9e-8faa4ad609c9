import React, { useState, useEffect } from 'react';
import {
  <PERSON>pography,
  Grid,
  Card,
  CardContent,
  Box,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  People,
  AccountBalance as Temple,
  PendingActions,
  CheckCircle,
} from '@mui/icons-material';
import { adminAPI } from '../../utils/api';

interface DashboardStats {
  users: {
    total: number;
    pending: number;
    approved: number;
  };
  mandals: {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
  };
}

const AdminStats: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await adminAPI.getDashboardStats();
        setStats(response.data);
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to fetch statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  if (!stats) {
    return <Alert severity="warning">No data available</Alert>;
  }

  const statCards = [
    {
      title: 'Total Users',
      value: stats.users.total,
      icon: <People sx={{ fontSize: 40, color: 'primary.main' }} />,
      color: 'primary.main',
    },
    {
      title: 'Pending Users',
      value: stats.users.pending,
      icon: <PendingActions sx={{ fontSize: 40, color: 'warning.main' }} />,
      color: 'warning.main',
    },
    {
      title: 'Total Mandals',
      value: stats.mandals.total,
      icon: <Temple sx={{ fontSize: 40, color: 'secondary.main' }} />,
      color: 'secondary.main',
    },
    {
      title: 'Pending Mandals',
      value: stats.mandals.pending,
      icon: <PendingActions sx={{ fontSize: 40, color: 'warning.main' }} />,
      color: 'warning.main',
    },
    {
      title: 'Approved Mandals',
      value: stats.mandals.approved,
      icon: <CheckCircle sx={{ fontSize: 40, color: 'success.main' }} />,
      color: 'success.main',
    },
    {
      title: 'Approved Users',
      value: stats.users.approved,
      icon: <CheckCircle sx={{ fontSize: 40, color: 'success.main' }} />,
      color: 'success.main',
    },
  ];

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Dashboard Overview
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Monitor and manage your Ganesh Darshan platform
      </Typography>

      <Grid container spacing={3}>
        {statCards.map((card, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card
              sx={{
                height: '100%',
                transition: 'transform 0.2s',
                '&:hover': {
                  transform: 'translateY(-2px)',
                },
              }}
            >
              <CardContent>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}
                >
                  <Box>
                    <Typography variant="h4" component="div" sx={{ color: card.color }}>
                      {card.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {card.title}
                    </Typography>
                  </Box>
                  <Box>{card.icon}</Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Quick Actions */}
      <Box sx={{ mt: 4 }}>
        <Typography variant="h5" gutterBottom>
          Quick Actions Needed
        </Typography>
        <Grid container spacing={2}>
          {stats.users.pending > 0 && (
            <Grid item xs={12} md={6}>
              <Alert severity="warning">
                <strong>{stats.users.pending}</strong> users are waiting for approval
              </Alert>
            </Grid>
          )}
          {stats.mandals.pending > 0 && (
            <Grid item xs={12} md={6}>
              <Alert severity="warning">
                <strong>{stats.mandals.pending}</strong> mandals are waiting for approval
              </Alert>
            </Grid>
          )}
          {stats.users.pending === 0 && stats.mandals.pending === 0 && (
            <Grid item xs={12}>
              <Alert severity="success">
                All pending items have been reviewed. Great job!
              </Alert>
            </Grid>
          )}
        </Grid>
      </Box>
    </Box>
  );
};

export default AdminStats;
