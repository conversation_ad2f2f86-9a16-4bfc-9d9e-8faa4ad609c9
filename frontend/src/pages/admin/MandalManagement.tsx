import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Chip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TablePagination,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  CheckCircle,
  Cancel,
  Search,
  Visibility,
} from '@mui/icons-material';
import { adminAPI } from '../../utils/api';
import { Mandal, ApiResponse } from '../../types';
import toast from 'react-hot-toast';

const MandalManagement: React.FC = () => {
  const [mandals, setMandals] = useState<Mandal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    mandal: Mandal | null;
    action: 'approve' | 'reject';
  }>({
    open: false,
    mandal: null,
    action: 'approve',
  });
  const [rejectReason, setRejectReason] = useState('');

  const fetchMandals = async () => {
    try {
      setLoading(true);
      const params: any = {
        page: page + 1,
        limit: rowsPerPage,
      };

      if (statusFilter !== 'all') {
        params.status = statusFilter;
      }

      if (categoryFilter !== 'all') {
        params.category = categoryFilter;
      }

      const response = await adminAPI.getAllMandals(params);
      setMandals(response.data.mandals);
      setTotal(response.data.pagination.total);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch mandals');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMandals();
  }, [page, rowsPerPage, statusFilter, categoryFilter]);

  const handleApprove = async (mandal: Mandal) => {
    try {
      await adminAPI.approveMandal(mandal._id);
      toast.success(`Mandal ${mandal.name} approved successfully`);
      fetchMandals();
    } catch (err: any) {
      toast.error(err.response?.data?.message || 'Failed to approve mandal');
    }
    setConfirmDialog({ open: false, mandal: null, action: 'approve' });
  };

  const handleReject = async (mandal: Mandal) => {
    try {
      await adminAPI.rejectMandal(mandal._id, rejectReason);
      toast.success(`Mandal ${mandal.name} rejected`);
      fetchMandals();
    } catch (err: any) {
      toast.error(err.response?.data?.message || 'Failed to reject mandal');
    }
    setConfirmDialog({ open: false, mandal: null, action: 'reject' });
    setRejectReason('');
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'approved':
        return <Chip label="Approved" color="success" size="small" />;
      case 'pending':
        return <Chip label="Pending" color="warning" size="small" />;
      case 'rejected':
        return <Chip label="Rejected" color="error" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  const getCategoryChip = (category: string) => {
    const colors: any = {
      mandal: 'primary',
      home: 'secondary',
      celebrity: 'info',
    };
    return <Chip label={category.charAt(0).toUpperCase() + category.slice(1)} color={colors[category]} size="small" />;
  };

  const filteredMandals = mandals.filter(mandal =>
    search === '' ||
    mandal.name.toLowerCase().includes(search.toLowerCase()) ||
    mandal.location.city.toLowerCase().includes(search.toLowerCase())
  );

  if (loading && mandals.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Mandal Management
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Review and manage mandal registrations
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
        <TextField
          placeholder="Search mandals..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
          sx={{ minWidth: 250 }}
        />
        <FormControl sx={{ minWidth: 150 }}>
          <InputLabel>Status</InputLabel>
          <Select
            value={statusFilter}
            label="Status"
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <MenuItem value="all">All</MenuItem>
            <MenuItem value="pending">Pending</MenuItem>
            <MenuItem value="approved">Approved</MenuItem>
            <MenuItem value="rejected">Rejected</MenuItem>
          </Select>
        </FormControl>
        <FormControl sx={{ minWidth: 150 }}>
          <InputLabel>Category</InputLabel>
          <Select
            value={categoryFilter}
            label="Category"
            onChange={(e) => setCategoryFilter(e.target.value)}
          >
            <MenuItem value="all">All</MenuItem>
            <MenuItem value="mandal">Mandal</MenuItem>
            <MenuItem value="home">Home</MenuItem>
            <MenuItem value="celebrity">Celebrity</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Mandals Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Category</TableCell>
              <TableCell>Location</TableCell>
              <TableCell>Owner</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Registered</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredMandals.map((mandal) => (
              <TableRow key={mandal._id}>
                <TableCell>
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      {mandal.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Est. {mandal.establishmentYear}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>{getCategoryChip(mandal.category)}</TableCell>
                <TableCell>
                  {mandal.location.city}, {mandal.location.state}
                </TableCell>
                <TableCell>
                  {typeof mandal.owner === 'object'
                    ? `${mandal.owner.firstName} ${mandal.owner.lastName}`
                    : 'Unknown'
                  }
                </TableCell>
                <TableCell>{getStatusChip(mandal.status)}</TableCell>
                <TableCell>
                  {new Date(mandal.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<Visibility />}
                      onClick={() => window.open(`/mandals/${mandal._id}`, '_blank')}
                    >
                      View
                    </Button>
                    {mandal.status === 'pending' && (
                      <>
                        <Button
                          size="small"
                          variant="contained"
                          color="success"
                          startIcon={<CheckCircle />}
                          onClick={() =>
                            setConfirmDialog({
                              open: true,
                              mandal,
                              action: 'approve',
                            })
                          }
                        >
                          Approve
                        </Button>
                        <Button
                          size="small"
                          variant="outlined"
                          color="error"
                          startIcon={<Cancel />}
                          onClick={() =>
                            setConfirmDialog({
                              open: true,
                              mandal,
                              action: 'reject',
                            })
                          }
                        >
                          Reject
                        </Button>
                      </>
                    )}
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component="div"
        count={total}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={(_, newPage) => setPage(newPage)}
        onRowsPerPageChange={(event) => {
          setRowsPerPage(parseInt(event.target.value, 10));
          setPage(0);
        }}
      />

      {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialog.open}
        onClose={() => {
          setConfirmDialog({ open: false, mandal: null, action: 'approve' });
          setRejectReason('');
        }}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {confirmDialog.action === 'approve' ? 'Approve Mandal' : 'Reject Mandal'}
        </DialogTitle>
        <DialogContent>
          <Typography sx={{ mb: 2 }}>
            Are you sure you want to {confirmDialog.action} mandal{' '}
            <strong>{confirmDialog.mandal?.name}</strong>?
          </Typography>
          {confirmDialog.action === 'reject' && (
            <TextField
              fullWidth
              multiline
              rows={3}
              label="Reason for rejection (optional)"
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              placeholder="Please provide a reason for rejection..."
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              setConfirmDialog({ open: false, mandal: null, action: 'approve' });
              setRejectReason('');
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              if (confirmDialog.mandal) {
                if (confirmDialog.action === 'approve') {
                  handleApprove(confirmDialog.mandal);
                } else {
                  handleReject(confirmDialog.mandal);
                }
              }
            }}
            color={confirmDialog.action === 'approve' ? 'success' : 'error'}
            variant="contained"
          >
            {confirmDialog.action === 'approve' ? 'Approve' : 'Reject'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MandalManagement;
