import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    primary: {
      main: '#fc5130', // Tomato - vibrant red-orange
      light: '#ff7961',
      dark: '#c41e3a',
      contrastText: '#fffaff',
    },
    secondary: {
      main: '#EEB71F', // Xanthous - golden yellow
      light: '#ffeb3b',
      dark: '#c49000',
      contrastText: '#050401',
    },
    background: {
      default: '#fffaff', // Ghost white
      paper: '#fffaff',
    },
    text: {
      primary: '#050401', // Black for primary text
      secondary: '#050401cc', // Black with opacity for secondary text
    },
    error: {
      main: '#fc5130',
    },
    warning: {
      main: '#EEB71F',
    },
    info: {
      main: '#2196F3',
    },
    success: {
      main: '#4CAF50',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 500,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 500,
      lineHeight: 1.5,
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 500,
      lineHeight: 1.5,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.5,
    },
    button: {
      textTransform: 'none',
      fontWeight: 500,
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          padding: '10px 24px',
          fontSize: '0.875rem',
          fontWeight: 500,
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 2px 8px rgba(252, 81, 48, 0.2)',
          },
        },
        contained: {
          '&:hover': {
            boxShadow: '0 4px 12px rgba(252, 81, 48, 0.3)',
          },
        },
        containedSecondary: {
          backgroundColor: '#EEB71F',
          color: '#050401',
          '&:hover': {
            backgroundColor: '#c49000',
            boxShadow: '0 4px 12px rgba(238, 183, 31, 0.3)',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          backgroundColor: '#fffaff',
          boxShadow: '0 2px 8px rgba(5, 4, 1, 0.08)',
          border: '1px solid rgba(5, 4, 1, 0.05)',
          '&:hover': {
            boxShadow: '0 4px 16px rgba(5, 4, 1, 0.12)',
            transform: 'translateY(-2px)',
            transition: 'all 0.2s ease-in-out',
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
            backgroundColor: '#fffaff',
            '& fieldset': {
              borderColor: 'rgba(5, 4, 1, 0.2)',
            },
            '&:hover fieldset': {
              borderColor: 'rgba(252, 81, 48, 0.5)',
            },
            '&.Mui-focused fieldset': {
              borderColor: '#fc5130',
            },
          },
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#fffaff',
          color: '#050401',
          boxShadow: '0 1px 3px rgba(5, 4, 1, 0.1)',
          borderBottom: '1px solid rgba(5, 4, 1, 0.05)',
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          fontWeight: 500,
        },
        colorPrimary: {
          backgroundColor: '#fc5130',
          color: '#fffaff',
        },
        colorSecondary: {
          backgroundColor: '#EEB71F',
          color: '#050401',
        },
      },
    },
  },
});

export default theme;
