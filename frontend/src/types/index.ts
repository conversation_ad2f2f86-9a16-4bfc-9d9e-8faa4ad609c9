export interface User {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  role: 'user' | 'admin' | 'superadmin';
  isActive: boolean;
  isApproved: boolean;
  avatar?: string;
  address: {
    street?: string;
    city: string;
    state: string;
    pincode?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface Mandal {
  _id: string;
  name: string;
  registrationNumber: string;
  establishmentYear: number;
  description: string;
  category: 'mandal' | 'home' | 'celebrity';
  location: {
    address: string;
    city: string;
    state: string;
    pincode: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  contactInfo: {
    email: string;
    phone: string;
    alternatePhone?: string;
    website?: string;
    socialMedia: {
      facebook?: string;
      instagram?: string;
      twitter?: string;
      youtube?: string;
    };
  };
  teamMembers: Array<{
    name: string;
    designation: string;
    phone?: string;
  }>;
  ganeshDetails: {
    murtiType: 'clay' | 'plaster_of_paris' | 'eco_friendly' | 'fiber' | 'metal' | 'stone' | 'other';
    height: string;
    weight: string;
    decorationType: 'traditional' | 'modern' | 'theme_based' | 'eco_friendly' | 'cultural' | 'mythological' | 'other';
    theme: string;
    description: string;
    specialFeatures: string[];
  };
  images: Array<{
    url: string;
    caption?: string;
    category: 'mandal' | 'ganesh' | 'decoration' | 'events' | 'team' | 'other';
    year: number;
    isPrimary: boolean;
  }>;
  facilities: {
    parking: boolean;
    wheelchairAccess: boolean;
    restrooms: boolean;
    foodStalls: boolean;
  };
  timings: {
    openTime: string;
    closeTime: string;
    specialTimings?: string;
  };
  donations: {
    acceptsDonations: boolean;
    donationMethods: string[];
    bankDetails?: {
      accountName: string;
      accountNumber: string;
      ifscCode: string;
      bankName: string;
    };
  };
  owner: string | User;
  status: 'pending' | 'approved' | 'rejected';
  isActive: boolean;
  views: number;
  likes: string[];
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  status: string;
  message: string;
  data: {
    user: User;
    token: string;
  };
}

export interface ApiResponse<T> {
  status: string;
  message?: string;
  data: T;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  address: {
    city: string;
    state: string;
  };
}
