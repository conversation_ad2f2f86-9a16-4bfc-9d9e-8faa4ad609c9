import React from 'react';
import { Box, Container, Typography, <PERSON>, Grid } from '@mui/material';

const Footer: React.FC = () => {
  return (
    <Box
      component="footer"
      sx={{
        backgroundColor: '#f8f8f8',
        borderTop: '1px solid rgba(5, 4, 1, 0.1)',
        py: 4,
        mt: 'auto',
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          <Grid item xs={12} md={4}>
            <Typography variant="h6" color="primary" gutterBottom>
              🕉️ <PERSON><PERSON><PERSON>
            </Typography>
            <Typography variant="body2" color="text.secondary">
              A platform dedicated to connecting devotees with Ganesh Mandals, 
              celebrating the divine presence of Lord <PERSON><PERSON><PERSON> in our communities.
            </Typography>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Typography variant="h6" gutterBottom>
              Quick Links
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Link href="/" color="text.secondary" underline="hover">
                Home
              </Link>
              <Link href="/mandals" color="text.secondary" underline="hover">
                Browse Mandals
              </Link>
              <Link href="/register" color="text.secondary" underline="hover">
                Register Your Mandal
              </Link>
            </Box>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Typography variant="h6" gutterBottom>
              Contact
            </Typography>
            <Typography variant="body2" color="text.secondary">
              For support and inquiries, please contact our team.
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Email: <EMAIL>
            </Typography>
          </Grid>
        </Grid>
        
        <Box
          sx={{
            borderTop: 1,
            borderColor: 'divider',
            mt: 4,
            pt: 2,
            textAlign: 'center',
          }}
        >
          <Typography variant="body2" color="text.secondary">
            © {new Date().getFullYear()} Ganesh Darshan. All rights reserved.
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
